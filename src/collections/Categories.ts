import type { CollectionConfig } from 'payload';

import { slugField } from '../fields/slug';
import { anyone } from '../access/anyone';
import { authenticated } from '../access/authenticated';

export const Categories: CollectionConfig = {
  slug: 'categories',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    group: 'Globals',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'German Title',
      admin: {
        description: 'Category name in German (main language)',
      },
    },
    {
      name: 'english',
      type: 'text',
      required: true,
      label: 'English Translation',
    },
    ...slugField(),
  ],
};
