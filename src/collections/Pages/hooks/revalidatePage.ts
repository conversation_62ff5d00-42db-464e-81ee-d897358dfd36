import type {
  CollectionAfterChangeHook,
  CollectionAfterDeleteHook,
} from 'payload';
import { revalidatePath, revalidateTag } from 'next/cache';

export const revalidatePage: CollectionAfterChangeHook = ({
  doc,
  previousDoc,
  req: { payload, context },
}) => {
  if (!context?.disableRevalidate) {
    // For published pages, revalidate the path
    if (doc._status === 'published') {
      const path = doc.slug === 'home' ? '/' : `/${doc.slug}`;

      payload.logger.info(`Revalidating page at path: ${path}`);

      revalidatePath(path);
      revalidateTag('pages-sitemap');
    }

    // If the page was previously published but now isn't, revalidate the old path
    if (previousDoc?._status === 'published' && doc._status !== 'published') {
      const oldPath =
        previousDoc.slug === 'home' ? '/' : `/${previousDoc.slug}`;

      payload.logger.info(`Revalidating old page at path: ${oldPath}`);

      revalidatePath(oldPath);
      revalidateTag('pages-sitemap');
    }
  }

  return doc;
};

export const revalidateDelete: CollectionAfterDeleteHook = ({
  doc,
  req: { payload, context },
}) => {
  if (!context?.disableRevalidate) {
    const path = doc?.slug === 'home' ? '/' : `/${doc?.slug}`;

    payload.logger.info(`Revalidating deleted page at path: ${path}`);

    revalidatePath(path);
    revalidateTag('pages-sitemap');
  }

  return doc;
};
