import {
  extractContentEnhanced,
  logFirecrawlSummary,
} from '@/lib/integrations/firecrawl/enhanced-client';
import { parseRSSFeed, type RSSItem } from '@/lib/integrations/rss/parser';
import { createCandidateArticle } from '@/lib/server/create-candidate';
import {
  checkUrlExists,
  createProcessedUrl,
} from '@/lib/server/processed-urls/index';
import {
  getActiveRSSFeeds,
  updateRSSFeedStats,
} from '@/lib/server/rss-feeds/index';
import type { Article, ProcessedUrl } from '@/lib/types';
import type { RssFeed } from '@/payload-types';

interface ProcessingResult {
  success: boolean;
  processed: number;
  accepted: number;
  rejected: number;
  errors: string[];
  details: ProcessingDetail[];
}

interface ProcessingDetail {
  url: string;
  title: string;
  status: 'accepted' | 'rejected' | 'error' | 'rate_limited';
  reason: string;
  articleId?: string;
}

export class RSSProcessingService {
  /**
   * Process all active RSS feeds
   * Now with frequency-aware processing - only processes feeds that are due based on their processingFrequency
   */
  async processAllFeeds(): Promise<ProcessingResult> {
    return this.processFeeds(false);
  }

  /**
   * Process all active RSS feeds, bypassing frequency checks (useful for testing)
   */
  async processAllFeedsForce(): Promise<ProcessingResult> {
    return this.processFeeds(true);
  }

  /**
   * Internal method to process feeds with optional frequency bypass
   */
  private async processFeeds(
    bypassFrequency: boolean
  ): Promise<ProcessingResult> {
    console.log('🔄 Starting RSS processing for all active feeds...');

    const result: ProcessingResult = {
      success: true,
      processed: 0,
      accepted: 0,
      rejected: 0,
      errors: [],
      details: [],
    };

    try {
      // Get all active RSS feeds from database
      const activeFeeds = await getActiveRSSFeeds();
      console.log(`📡 Found ${activeFeeds.length} active RSS feeds`);

      // Filter feeds based on processingFrequency - only process feeds that are due
      const now = Date.now();
      let feedsDueForProcessing = activeFeeds;

      if (bypassFrequency) {
        console.log(
          '🚀 Bypassing frequency checks - processing all active feeds'
        );
      } else {
        feedsDueForProcessing = activeFeeds.filter(feed => {
          const feedData = feed as any;
          const processingFrequency = feedData.processingFrequency || 360; // Default to 6 hours if not set
          const lastProcessed = feedData.lastProcessed;

          // If never processed, it's due now
          if (!lastProcessed) {
            console.log(`⏰ ${feed.name}: Never processed - due now`);
            return true;
          }

          // Calculate time since last processing
          const lastProcessedTime = new Date(lastProcessed).getTime();
          const timeSinceLastProcessed = now - lastProcessedTime;
          const frequencyInMs = processingFrequency * 60 * 1000; // Convert minutes to milliseconds
          const isDue = timeSinceLastProcessed >= frequencyInMs;

          // Log frequency check results
          const timeSinceHours =
            Math.round((timeSinceLastProcessed / (60 * 60 * 1000)) * 10) / 10;
          const frequencyHours =
            Math.round((processingFrequency / 60) * 10) / 10;

          if (isDue) {
            console.log(
              `⏰ ${feed.name}: ${timeSinceHours}h since last processing, ${frequencyHours}h frequency - DUE NOW`
            );
          } else {
            const timeUntilDue = frequencyInMs - timeSinceLastProcessed;
            const hoursUntilDue =
              Math.round((timeUntilDue / (60 * 60 * 1000)) * 10) / 10;
            console.log(
              `⏰ ${feed.name}: ${timeSinceHours}h since last processing, ${frequencyHours}h frequency - ${hoursUntilDue}h until due`
            );
          }

          return isDue;
        });

        console.log(
          `📊 Frequency check results: ${feedsDueForProcessing.length}/${activeFeeds.length} feeds due for processing`
        );

        if (feedsDueForProcessing.length === 0) {
          console.log('🎯 No feeds due for processing at this time');
          return result;
        }
      }

      // Sort feeds by priority (high → medium → low)
      const sortedFeeds = feedsDueForProcessing.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return (
          (priorityOrder[b.priority as keyof typeof priorityOrder] || 0) -
          (priorityOrder[a.priority as keyof typeof priorityOrder] || 0)
        );
      });

      for (let i = 0; i < sortedFeeds.length; i++) {
        const feed = sortedFeeds[i];
        const feedData = feed as any;
        console.log(
          `\n📰 [${i + 1}/${sortedFeeds.length}] Processing: ${feed.name}`
        );
        console.log(
          `   Priority: ${feed.priority} | Language: ${feed.language} | Frequency: ${Math.round(((feedData.processingFrequency || 360) / 60) * 10) / 10}h`
        );
        console.log(`   URL: ${feed.url}`);

        try {
          const feedResult = await this.processSingleFeed(feed);

          // Log feed-specific results
          console.log(
            `   📊 Feed Results: ${feedResult.accepted} accepted, ${feedResult.rejected} rejected, ${feedResult.errors.length} errors`
          );

          // Aggregate results
          result.processed += feedResult.processed;
          result.accepted += feedResult.accepted;
          result.rejected += feedResult.rejected;
          result.details.push(...feedResult.details);

          if (!feedResult.success) {
            result.errors.push(
              `Feed ${feed.name}: ${feedResult.errors.join(', ')}`
            );
          }

          // Update feed statistics
          await updateRSSFeedStats(feed.id.toString(), {
            processed: feedResult.processed,
            accepted: feedResult.accepted,
            articlesFoundSinceLastSuccessful: feedResult.accepted,
            success: feedResult.success && feedResult.errors.length === 0,
            errorMessage:
              feedResult.errors.length > 0
                ? feedResult.errors.join(', ')
                : undefined,
          });
        } catch (error: any) {
          const errorMsg = `Failed to process feed ${feed.name}: ${error.message}`;
          console.error('❌', errorMsg);
          result.errors.push(errorMsg);
          result.success = false;
        }
      }

      console.log('\n🎯 Final Processing Summary:');
      console.log(`   📄 Total items processed: ${result.processed}`);
      console.log(`   ✅ Articles accepted: ${result.accepted}`);
      console.log(`   ❌ Articles rejected: ${result.rejected}`);
      console.log(`   ⚠️  Errors encountered: ${result.errors.length}`);
      console.log(
        `   📈 Success rate: ${result.processed > 0 ? Math.round((result.accepted / result.processed) * 100) : 0}%`
      );

      // Log Firecrawl API usage summary
      logFirecrawlSummary();

      return result;
    } catch (error: any) {
      console.error('❌ RSS processing failed:', error);
      result.success = false;
      result.errors.push(`Global processing error: ${error.message}`);
      return result;
    }
  }

  /**
   * Build feed-specific extraction options for Firecrawl
   * Updated to properly handle all feed-specific settings
   */
  private buildFeedSpecificOptions(feed: RssFeed) {
    const options: any = {};

    // Apply feed-specific Firecrawl options if configured
    if ((feed as any).firecrawlOptions) {
      const firecrawlOpts = (feed as any).firecrawlOptions;

      // Use feed-specific settings, or defaults if not specified
      if (firecrawlOpts.removeBase64Images !== undefined) {
        options.removeBase64Images = firecrawlOpts.removeBase64Images;
      } else {
        options.removeBase64Images = true; // Default enabled
      }

      if (firecrawlOpts.blockAds !== undefined) {
        options.blockAds = firecrawlOpts.blockAds;
      } else {
        options.blockAds = true; // Default enabled
      }

      // Handle excludeTags array structure
      if (firecrawlOpts.excludeTags && firecrawlOpts.excludeTags.length > 0) {
        options.excludeTags = firecrawlOpts.excludeTags
          .map((item: any) => item.tag)
          .filter(Boolean);
        console.log(
          `📋 Using ${options.excludeTags.length} custom exclude tags for ${feed.name}`
        );
      }

      // Handle includeTags array structure
      if (firecrawlOpts.includeTags && firecrawlOpts.includeTags.length > 0) {
        options.includeTags = firecrawlOpts.includeTags
          .map((item: any) => item.tag)
          .filter(Boolean);
        console.log(
          `📋 Using ${options.includeTags.length} custom include tags for ${feed.name}`
        );
      }
    } else {
      // No firecrawl options specified, use defaults
      options.removeBase64Images = true;
      options.blockAds = true;
    }

    // Apply processing options if configured
    if ((feed as any).processingOptions) {
      const procOpts = (feed as any).processingOptions;

      if (procOpts.customTimeout) {
        options.timeout = procOpts.customTimeout * 1000; // Convert to milliseconds
        console.log(
          `⏱️ Using custom timeout: ${procOpts.customTimeout}s for ${feed.name}`
        );
      }

      if (procOpts.enableStealth) {
        options.forceStealth = true;
        console.log(`🥷 Stealth mode enabled for ${feed.name}`);
      }
    }

    // Log the final options being used
    if (Object.keys(options).length > 0) {
      console.log(`🎛️ Feed-specific options for ${feed.name}:`, {
        removeBase64Images: options.removeBase64Images,
        blockAds: options.blockAds,
        excludeTags: options.excludeTags?.length || 0,
        includeTags: options.includeTags?.length || 0,
        customTimeout: options.timeout ? `${options.timeout}ms` : 'default',
        stealth: options.forceStealth || false,
      });
    }

    return options;
  }

  /**
   * Process a single RSS feed with new cost-optimized flow:
   * 1. Parse ALL RSS items (no limit)
   * 2. Filter ALL for duplicates and keywords (no cost)
   * 3. Scrape up to maxFirecrawlScrape (Firecrawl costs)
   * 4. Send up to maxArticlesPerRun to OpenAI (OpenAI costs)
   *
   * NEW: Added parallel processing for significant performance improvement
   */
  async processSingleFeed(feed: RssFeed): Promise<ProcessingResult> {
    const result: ProcessingResult = {
      success: true,
      processed: 0,
      accepted: 0,
      rejected: 0,
      errors: [],
      details: [],
    };

    try {
      // Parse RSS feed - get ALL items
      console.log(`   🔍 Parsing RSS feed...`);
      const rssItems = await parseRSSFeed(feed.url);
      console.log(`   📄 Found ${rssItems.length} items in feed`);

      // Phase 1: Filter ALL items for duplicates and keywords (no cost operations)
      console.log(
        `   🔍 Phase 1: Filtering for duplicates and keywords (all ${rssItems.length} items)...`
      );

      const candidateArticles = [];
      let duplicateCount = 0;
      let keywordRejectCount = 0;

      for (let i = 0; i < rssItems.length; i++) {
        const item = rssItems[i];
        const url = item.link;
        const title = item.title || 'Untitled';

        if (!url) {
          console.log(`   ⚠️  Item ${i + 1}: No URL provided, skipping...`);
          continue;
        }

        // Check if URL already processed
        const urlExists = await checkUrlExists(url);
        if (urlExists) {
          duplicateCount++;
          result.details.push({
            url,
            title,
            status: 'rejected',
            reason: 'URL already processed',
          });
          result.processed++;
          result.rejected++;
          continue;
        }

        // Check keywords
        const hasRelevantKeywords = await this.checkBasicKeywords(
          title,
          item.description || '',
          feed
        );

        if (!hasRelevantKeywords) {
          keywordRejectCount++;

          // Mark as processed but rejected
          await createProcessedUrl({
            url,
            title,
            feedId: feed.id?.toString() || 'unknown',
            status: 'rejected',
            reason: 'No relevant keywords found',
            publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
          });

          result.details.push({
            url,
            title,
            status: 'rejected',
            reason: 'No relevant keywords in title/description',
          });
          result.processed++;
          result.rejected++;
          continue;
        }

        // Article passed all free filters - add to candidates
        candidateArticles.push(item);
      }

      console.log(
        `   📊 Phase 1 Results: ${candidateArticles.length} candidates (${duplicateCount} duplicates, ${keywordRejectCount} keyword rejects)`
      );

      if (candidateArticles.length === 0) {
        console.log(`   📭 No candidate articles found after filtering`);
        return result;
      }

      // Phase 2: Apply Firecrawl scraping limit
      const maxFirecrawlScrape =
        (feed as any).processingOptions?.maxFirecrawlScrape ||
        candidateArticles.length;
      const articlesToScrape = candidateArticles.slice(0, maxFirecrawlScrape);

      if (maxFirecrawlScrape < candidateArticles.length) {
        console.log(
          `   🔥 Phase 2: Limiting Firecrawl scraping to ${maxFirecrawlScrape} articles (${candidateArticles.length - maxFirecrawlScrape} candidates skipped)`
        );
      } else {
        console.log(
          `   🔥 Phase 2: Scraping all ${articlesToScrape.length} candidate articles with Firecrawl...`
        );
      }

      // Phase 2: Parallel Firecrawl content extraction
      const scrapedArticles = [];
      let firecrawlFailCount = 0;

      // NEW: Process Firecrawl extractions in parallel (3 concurrent max to avoid rate limits)
      const FIRECRAWL_CONCURRENCY = 3;
      const firecrawlPromises = [];

      for (let i = 0; i < articlesToScrape.length; i += FIRECRAWL_CONCURRENCY) {
        const batch = articlesToScrape.slice(i, i + FIRECRAWL_CONCURRENCY);

        const batchPromises = batch.map(async (item, batchIndex) => {
          const globalIndex = i + batchIndex;
          const url = item.link;
          const title = item.title || 'Untitled';

          if (!url) {
            console.log(
              `   ⚠️  Item ${globalIndex + 1}: No URL provided, skipping...`
            );
            return null;
          }

          console.log(
            `   🔍 [${globalIndex + 1}/${articlesToScrape.length}] Scraping: ${title.substring(0, 60)}...`
          );

          try {
            // Extract content using Firecrawl
            const feedOptions = this.buildFeedSpecificOptions(feed);
            const enhancedResult = await extractContentEnhanced(
              url,
              feedOptions
            );

            const contentResult = {
              success: enhancedResult.success,
              content:
                enhancedResult.formats.html ||
                enhancedResult.formats.markdown ||
                '',
              title: enhancedResult.metadata?.title,
              author: enhancedResult.metadata?.author,
              publishedDate: enhancedResult.metadata?.publishDate
                ? new Date(enhancedResult.metadata.publishDate)
                : undefined,
              error:
                enhancedResult.errors.length > 0
                  ? enhancedResult.errors.join(', ')
                  : undefined,
              metadata: {
                wordCount: enhancedResult.metadata?.wordCount || 0,
                readingTime: enhancedResult.metadata?.readingTime || 0,
              },
            };

            if (!contentResult.success || !contentResult.content) {
              const isRateLimited =
                contentResult.error?.includes('Rate limited') ||
                contentResult.error?.includes('429');
              const status = isRateLimited ? 'pending' : 'error';
              const reason = isRateLimited
                ? 'Rate limited - will retry later'
                : contentResult.error || 'Content extraction failed';

              await createProcessedUrl({
                url,
                title,
                feedId: feed.id?.toString() || 'unknown',
                status,
                reason,
                publicationDate: item.pubDate
                  ? new Date(item.pubDate)
                  : undefined,
              });

              result.details.push({
                url,
                title,
                status: isRateLimited ? 'rate_limited' : 'error',
                reason,
              });
              result.processed++;
              result.errors.push(
                `Content extraction failed for ${title}: ${reason}`
              );
              return { failed: true, reason };
            }

            // Successfully scraped
            return {
              item,
              contentResult,
              url,
              title,
              failed: false,
            };
          } catch (error: any) {
            console.error(`❌ Firecrawl error for ${title}:`, error);
            result.errors.push(
              `Firecrawl error for ${title}: ${error.message}`
            );
            return { failed: true, reason: error.message };
          }
        });

        // Wait for this batch to complete before starting the next batch
        const batchResults = await Promise.all(batchPromises);

        // Process batch results
        for (const batchResult of batchResults) {
          if (batchResult) {
            if (batchResult.failed) {
              firecrawlFailCount++;
            } else {
              scrapedArticles.push(batchResult);
            }
          }
        }

        // Small delay between batches to avoid overwhelming Firecrawl
        if (i + FIRECRAWL_CONCURRENCY < articlesToScrape.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log(
        `   📊 Phase 2 Results: ${scrapedArticles.length} articles successfully scraped (${firecrawlFailCount} failed)`
      );

      if (scrapedArticles.length === 0) {
        console.log(
          `   📭 No articles successfully scraped - skipping OpenAI processing`
        );
        return result;
      }

      // Phase 3: Apply OpenAI processing limit
      const maxArticlesPerRun =
        (feed as any).processingOptions?.maxArticlesPerRun ||
        scrapedArticles.length;
      const articlesToProcess = scrapedArticles.slice(0, maxArticlesPerRun);

      if (maxArticlesPerRun < scrapedArticles.length) {
        console.log(
          `   🤖 Phase 3: Limiting OpenAI processing to ${maxArticlesPerRun} articles (${scrapedArticles.length - maxArticlesPerRun} scraped articles skipped)`
        );
      } else {
        console.log(
          `   🤖 Phase 3: Processing all ${articlesToProcess.length} scraped articles with OpenAI...`
        );
      }

      // Phase 3: Parallel OpenAI processing
      const OPENAI_CONCURRENCY = 2; // Conservative to avoid rate limits

      for (let i = 0; i < articlesToProcess.length; i += OPENAI_CONCURRENCY) {
        const batch = articlesToProcess.slice(i, i + OPENAI_CONCURRENCY);

        const openaiPromises = batch.map(async (articleData, batchIndex) => {
          const globalIndex = i + batchIndex;

          // Type-safe destructuring with null checks
          if (
            !articleData ||
            !articleData.item ||
            !articleData.contentResult ||
            !articleData.url ||
            !articleData.title
          ) {
            console.error(
              `❌ Invalid article data at index ${globalIndex + 1}`
            );
            return { success: false, error: 'Invalid article data' };
          }

          const { item, contentResult, url, title } = articleData;

          console.log(
            `   📝 [${globalIndex + 1}/${articlesToProcess.length}] Creating candidate article: ${title.substring(0, 60)}...`
          );

          try {
            // Extract feed processing options
            const feedProcessingOptions = {
              skipEnhancement:
                (feed as any).processingOptions?.skipEnhancement || false,
              skipTranslation:
                (feed as any).processingOptions?.skipTranslation || false,
              maxArticlesPerRun: (feed as any).processingOptions
                ?.maxArticlesPerRun,
              customTimeout: (feed as any).processingOptions?.customTimeout,
              enableStealth:
                (feed as any).processingOptions?.enableStealth || false,
              feedLanguage: (feed as any).language || 'de',
            };

            const articleResult = await createCandidateArticle(
              {
                title,
                content: contentResult.content || '',
                sourceUrl: url,
                sourceFeed: feed.id?.toString() || 'unknown',
                publishedDate: item.pubDate
                  ? new Date(item.pubDate)
                  : new Date(),
                author: item.author || undefined,
              },
              feedProcessingOptions
            );

            // Mark URL as processed and accepted
            await createProcessedUrl({
              url,
              title,
              feedId: feed.id?.toString() || 'unknown',
              status: 'accepted',
              reason: 'Keywords matched',
              articleId: articleResult.id,
              publicationDate: item.pubDate
                ? new Date(item.pubDate)
                : undefined,
            });

            result.details.push({
              url,
              title,
              status: 'accepted',
              reason: 'Keywords matched',
              articleId: articleResult.id,
            });
            result.processed++;
            result.accepted++;

            console.log(`      ✅ ACCEPTED: ${title}`);
            return { success: true };
          } catch (error: any) {
            const errorMsg = `Failed to create candidate article for ${title}: ${error.message}`;
            console.error('❌', errorMsg);
            result.errors.push(errorMsg);
            result.details.push({
              url,
              title,
              status: 'error',
              reason: error.message,
            });
            result.processed++;
            return { success: false, error: error.message };
          }
        });

        // Wait for this batch to complete
        await Promise.all(openaiPromises);

        // Small delay between batches to respect rate limits
        if (i + OPENAI_CONCURRENCY < articlesToProcess.length) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      console.log(
        `   🎯 Final Results: ${result.accepted} accepted, ${result.rejected} rejected, ${result.errors.length} errors`
      );

      return result;
    } catch (error: any) {
      console.error(`❌ Failed to parse RSS feed ${feed.url}:`, error);
      result.success = false;
      result.errors.push(`RSS parsing error: ${error.message}`);
      return result;
    }
  }

  /**
   * Process a single RSS item through the complete pipeline
   */
  private async processRSSItem(
    item: RSSItem,
    feed: RssFeed
  ): Promise<ProcessingDetail> {
    const url = item.link;
    const title = item.title || 'Untitled';

    if (!url) {
      return {
        url: 'unknown',
        title,
        status: 'error',
        reason: 'No URL provided',
      };
    }

    // Step 1: Check if URL already processed
    const urlExists = await checkUrlExists(url);
    if (urlExists) {
      return {
        url,
        title,
        status: 'rejected',
        reason: 'URL already processed',
      };
    }

    // Step 2: Basic keyword filtering using predefined keywords
    const hasRelevantKeywords = await this.checkBasicKeywords(
      title,
      item.description || '',
      feed
    );
    if (!hasRelevantKeywords) {
      // Mark as processed but rejected
      await createProcessedUrl({
        url,
        title,
        feedId: feed.id.toString(),
        status: 'rejected',
        reason: 'No relevant keywords found',
        publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
      });

      return {
        url,
        title,
        status: 'rejected',
        reason: 'No relevant keywords in title/description',
      };
    }

    // Step 3: Extract full content using Enhanced Firecrawl
    console.log(`      🔍 Extracting content with enhanced client...`);
    const feedOptions = this.buildFeedSpecificOptions(feed);
    const enhancedResult = await extractContentEnhanced(url, feedOptions);

    // Convert enhanced result to legacy format for compatibility
    const contentResult = {
      success: enhancedResult.success,
      content:
        enhancedResult.formats.html || enhancedResult.formats.markdown || '',
      title: enhancedResult.metadata?.title,
      author: enhancedResult.metadata?.author,
      publishedDate: enhancedResult.metadata?.publishDate
        ? new Date(enhancedResult.metadata.publishDate)
        : undefined,
      error:
        enhancedResult.errors.length > 0
          ? enhancedResult.errors.join(', ')
          : undefined,
      metadata: {
        wordCount: enhancedResult.metadata?.wordCount || 0,
        readingTime: enhancedResult.metadata?.readingTime || 0,
      },
    };

    if (!contentResult.success || !contentResult.content) {
      const isRateLimited =
        contentResult.error?.includes('Rate limited') ||
        contentResult.error?.includes('429');

      // For rate limiting, we want to mark as pending retry rather than error
      const status = isRateLimited ? 'pending' : 'error';
      const reason = isRateLimited
        ? 'Rate limited - will retry later'
        : contentResult.error || 'Content extraction failed';

      await createProcessedUrl({
        url,
        title,
        feedId: feed.id.toString(),
        status,
        reason,
        publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
      });

      if (isRateLimited) {
        console.log(`      ⏳ Rate limited for ${url} - marked for retry`);
      }

      return {
        url,
        title,
        status: isRateLimited ? 'rate_limited' : 'error',
        reason,
      };
    }

    // Step 4: Create candidate article (keywords already matched)
    console.log(`      📝 Creating candidate article`);

    // Extract feed processing options
    const feedProcessingOptions = {
      skipEnhancement:
        (feed as any).processingOptions?.skipEnhancement || false,
      skipTranslation:
        (feed as any).processingOptions?.skipTranslation || false,
      maxArticlesPerRun: (feed as any).processingOptions?.maxArticlesPerRun,
      customTimeout: (feed as any).processingOptions?.customTimeout,
      enableStealth: (feed as any).processingOptions?.enableStealth || false,
      feedLanguage: (feed as any).language || 'de', // Use feed's language setting
    };

    const articleResult = await createCandidateArticle(
      {
        title,
        content: contentResult.content,
        sourceUrl: url,
        sourceFeed: feed.id.toString(),
        publishedDate: item.pubDate ? new Date(item.pubDate) : new Date(),
        author: item.author || undefined,
      },
      feedProcessingOptions
    );

    // Mark URL as processed and accepted
    await createProcessedUrl({
      url,
      title,
      feedId: feed.id.toString(),
      status: 'accepted',
      reason: 'Keywords matched',
      articleId: articleResult.id,
      publicationDate: item.pubDate ? new Date(item.pubDate) : undefined,
    });

    return {
      url,
      title,
      status: 'accepted',
      reason: 'Keywords matched',
      articleId: articleResult.id,
    };
  }

  /**
   * Enhanced keyword filtering with feed-specific options
   * Supports strict matching and feed-specific keywords
   * NEW: Added content quality pre-filtering to reduce rejection rate
   */
  private async checkBasicKeywords(
    title: string,
    description: string,
    feed: RssFeed
  ): Promise<boolean> {
    try {
      // NEW: Pre-filter for content quality and relevance
      const contentQualityCheck = this.checkContentQuality(title, description);
      if (!contentQualityCheck.isQualityContent) {
        console.log(
          `⚠️ Low quality content filtered: ${contentQualityCheck.reason}`
        );
        return false;
      }

      const { getPayload } = await import('payload');
      const config = await import('@payload-config');
      const payload = await getPayload({ config: config.default });

      // Get feed-specific keyword filtering options
      const feedOptions = (feed as any).keywordFiltering || {};
      const strictMatching = feedOptions.strictKeywordMatching || false;
      const customKeywords = feedOptions.customKeywords || [];

      // Get all active keywords from database (global keywords)
      const allKeywords = await payload.find({
        collection: 'keywords',
        where: {
          isActive: { equals: true },
        },
        limit: 1000,
      });

      const text = `${title} ${description}`.toLowerCase();

      // Combine global keywords with feed-specific keywords
      const globalKeywords = allKeywords.docs;
      const allKeywordsToCheck: any[] = [...globalKeywords];

      // Add feed-specific keywords to the check
      if (customKeywords.length > 0) {
        console.log(
          `🔍 Using ${customKeywords.length} feed-specific keywords for ${feed.name}`
        );
        customKeywords.forEach((customKeyword: any) => {
          allKeywordsToCheck.push({
            keyword: customKeyword.keyword,
            englishKeyword: customKeyword.englishKeyword,
            weight: customKeyword.weight || 5,
            isCustom: true,
          });
        });
      }

      let foundKeywords: any[] = [];
      let totalScore = 0;

      // Check each keyword (global + feed-specific)
      for (const dbKeyword of allKeywordsToCheck) {
        const germanKeyword = dbKeyword.keyword.toLowerCase();
        const englishKeyword = dbKeyword.englishKeyword.toLowerCase();
        const weight = (dbKeyword as any).weight || 5;
        const isCustom = (dbKeyword as any).isCustom || false;

        let keywordFound = false;
        let foundVersion = '';

        if (strictMatching) {
          // Strict matching: exact word boundaries
          const germanRegex = new RegExp(`\\b${germanKeyword}\\b`, 'i');
          const englishRegex = new RegExp(`\\b${englishKeyword}\\b`, 'i');

          if (germanRegex.test(text)) {
            keywordFound = true;
            foundVersion = germanKeyword;
          } else if (englishRegex.test(text)) {
            keywordFound = true;
            foundVersion = englishKeyword;
          }
        } else {
          // Standard matching: partial matches
          if (text.includes(germanKeyword)) {
            keywordFound = true;
            foundVersion = germanKeyword;
          } else if (text.includes(englishKeyword)) {
            keywordFound = true;
            foundVersion = englishKeyword;
          }
        }

        if (keywordFound) {
          foundKeywords.push({
            keyword: dbKeyword.keyword,
            englishKeyword: dbKeyword.englishKeyword,
            foundVersion,
            weight,
            isCustom,
          });
          totalScore += weight;

          const sourceType = isCustom ? 'feed-specific' : 'global';
          console.log(
            `✅ Found ${sourceType} keyword: "${foundVersion}" (weight: ${weight})`
          );
        }
      }

      // NEW: Enhanced decision logic with scoring threshold
      const hasMatch = foundKeywords.length > 0;
      const scoreThreshold = feedOptions.minimumScore || 5; // Default minimum score
      const meetsThreshold = totalScore >= scoreThreshold;

      if (hasMatch && meetsThreshold) {
        console.log(
          `🎯 Keyword matching result: ${foundKeywords.length} keywords found (score: ${totalScore}/${scoreThreshold})`
        );
        if (strictMatching) {
          console.log(`🔍 Used strict matching mode`);
        }
        return true;
      } else if (hasMatch && !meetsThreshold) {
        console.log(
          `⚠️ Keywords found but score too low: ${totalScore}/${scoreThreshold}`
        );
        return false;
      }

      return false;
    } catch (error: any) {
      console.error(
        '❌ Failed to check keywords from database:',
        error.message
      );
      // Fallback to basic check for common financial terms
      const text = `${title} ${description}`.toLowerCase();
      const fallbackKeywords = [
        'aktien',
        'börse',
        'investment',
        'wirtschaft',
        'finanzen',
        'stock',
        'market',
        'finance',
      ];
      return fallbackKeywords.some(keyword => text.includes(keyword));
    }
  }

  /**
   * NEW: Content quality pre-filtering to reduce processing of low-value content
   */
  private checkContentQuality(
    title: string,
    description: string
  ): {
    isQualityContent: boolean;
    reason?: string;
  } {
    const combinedText = `${title} ${description}`.toLowerCase();

    // Filter out very short content
    if (combinedText.length < 50) {
      return { isQualityContent: false, reason: 'Content too short' };
    }

    // Filter out promotional/spam content patterns
    const spamPatterns = [
      /\b(sale|discount|offer|deal|free|win|prize|click here|buy now)\b/i,
      /\b(advertisement|sponsored|promo|marketing)\b/i,
      /\b(earn money|make money|get rich|work from home)\b/i,
    ];

    for (const pattern of spamPatterns) {
      if (pattern.test(combinedText)) {
        return {
          isQualityContent: false,
          reason: 'Promotional content detected',
        };
      }
    }

    // Filter out content with excessive punctuation (often spam)
    const punctuationRatio =
      (combinedText.match(/[!?]{2,}/g) || []).length / combinedText.length;
    if (punctuationRatio > 0.05) {
      return { isQualityContent: false, reason: 'Excessive punctuation' };
    }

    // Check for financial relevance indicators
    const financialIndicators = [
      'market',
      'stock',
      'investment',
      'financial',
      'economy',
      'trading',
      'company',
      'business',
      'revenue',
      'profit',
      'earnings',
      'economic',
      'bank',
      'finance',
      'money',
      'price',
      'value',
      'growth',
      'industry',
      'aktien',
      'börse',
      'wirtschaft',
      'unternehmen',
      'finanzen',
      'markt',
    ];

    const hasFinancialContent = financialIndicators.some(indicator =>
      combinedText.includes(indicator)
    );

    if (!hasFinancialContent) {
      return {
        isQualityContent: false,
        reason: 'No financial indicators found',
      };
    }

    return { isQualityContent: true };
  }
}

// Export singleton instance
export const rssProcessingService = new RSSProcessingService();
