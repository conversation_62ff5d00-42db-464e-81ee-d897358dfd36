import config from '@payload-config';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import { htmlToLexical } from '@/lib/utils/html-to-lexical';
import { lexicalToText } from '@/lib/utils/lexical';
import { translateToGerman } from '@/lib/integrations/openai/german-translation';

export async function POST(request: NextRequest) {
  try {
    const { articleId, currentEnglishContent } = await request.json();

    if (!articleId) {
      console.error('❌ No articleId provided in request');
      return NextResponse.json(
        { success: false, error: 'Article ID is required' },
        { status: 400 }
      );
    }

    console.log(`🚀 Starting German translation for article: ${articleId}`);

    const payload = await getPayload({ config });

    // Fetch the article
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
    });

    if (!article) {
      console.error(`❌ Article not found with ID: ${articleId}`);
      return NextResponse.json(
        { success: false, error: 'Article not found' },
        { status: 404 }
      );
    }

    console.log(`📄 Article found: ${article.title}`);
    console.log(`📊 Article workflow stage: ${article.workflowStage}`);
    console.log(`📊 Article type: ${article.articleType}`);

    // Validate article is eligible for translation
    if (article.articleType !== 'generated') {
      console.error(`❌ Article type not eligible: ${article.articleType}`);
      return NextResponse.json(
        { success: false, error: 'Only generated articles can be translated' },
        { status: 400 }
      );
    }

    // Validate article has enhanced English content
    if (
      !article.englishTab?.enhancedTitle ||
      !article.englishTab?.enhancedContent
    ) {
      console.error('❌ Article missing enhanced English content');
      console.log('  Enhanced title:', !!article.englishTab?.enhancedTitle);
      console.log('  Enhanced content:', !!article.englishTab?.enhancedContent);
      return NextResponse.json(
        {
          success: false,
          error:
            'Article must have enhanced English content before translation',
        },
        { status: 400 }
      );
    }

    // Allow re-translation if German translation already exists
    const isReTranslation = !!article.germanTab?.germanTitle;
    if (isReTranslation) {
      console.log('🔄 Re-translating existing German content');
      console.log('  Previous German title:', article.germanTab?.germanTitle);
      console.log('  Will translate from current English content');
    } else {
      console.log('🆕 Initial German translation');
    }

    // Extract English content for translation
    // Use current form data if provided (for real-time translation), otherwise use database data
    let englishTitle,
      englishSummary,
      englishContentText,
      englishKeyInsights,
      englishKeywords;

    if (currentEnglishContent) {
      console.log(
        '🔄 Using current form data for translation (includes unsaved changes)'
      );

      englishTitle = currentEnglishContent.title;
      englishSummary = currentEnglishContent.summary || '';
      englishContentText = currentEnglishContent.content
        ? lexicalToText(currentEnglishContent.content)
        : '';
      englishKeyInsights = currentEnglishContent.keyInsights
        ? currentEnglishContent.keyInsights.map((item: any) =>
            typeof item === 'string' ? item : item.insight
          )
        : [];
      englishKeywords = currentEnglishContent.keywords
        ? currentEnglishContent.keywords.map((item: any) =>
            typeof item === 'string' ? item : item.keyword
          )
        : [];
    } else {
      console.log('📄 Using database data for translation');

      englishTitle = article.englishTab.enhancedTitle;
      englishSummary = article.englishTab.enhancedSummary || '';
      englishContentText = lexicalToText(article.englishTab.enhancedContent);
      englishKeyInsights = article.englishTab.enhancedKeyInsights
        ? article.englishTab.enhancedKeyInsights.map(
            (item: any) => item.insight
          )
        : [];
      englishKeywords =
        article.englishTab?.keywords?.map((item: any) => item.keyword) || [];
    }

    // Validate we have the required content for translation
    if (!englishTitle || !englishContentText) {
      console.error('❌ Missing required English content for translation');
      console.log('  Title present:', !!englishTitle);
      console.log('  Content present:', !!englishContentText);
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required English title or content for translation',
        },
        { status: 400 }
      );
    }

    console.log('📄 Processing English content for translation:');
    console.log(
      '  Source:',
      currentEnglishContent ? 'Current form data' : 'Database'
    );
    console.log('  Title:', englishTitle);
    console.log('  Content length:', englishContentText.length, 'characters');
    console.log('  Key insights:', englishKeyInsights.length, 'insights');
    console.log('  Keywords:', englishKeywords.length, 'keywords');

    // Perform German translation
    const translationResult = await translateToGerman(
      {
        title: englishTitle,
        summary: englishSummary,
        content: englishContentText,
        keyInsights: englishKeyInsights,
        keywords: englishKeywords,
      },
      {
        temperature: 0.3, // Lower temperature for more literal translations
        includeProcessingMetadata: true,
      }
    );

    if (!translationResult.success || !translationResult.data) {
      console.error('❌ German translation failed:', translationResult.error);
      return NextResponse.json(
        {
          success: false,
          error: `German translation failed: ${translationResult.error}`,
        },
        { status: 500 }
      );
    }

    console.log('✅ German translation successful');
    console.log(
      `📊 Performance: ${translationResult.metrics.processingTime}ms`
    );

    // Extract translated German content
    const translatedData = translationResult.data;
    const germanTitle = translatedData.germanTitle;
    const germanSummary = translatedData.germanSummary;
    const germanContent = translatedData.germanContent;
    const germanKeyInsights = translatedData.germanKeyInsights;
    const germanKeywords = translatedData.germanKeywords;

    console.log('📝 German translation parsing:');
    console.log('  Title:', germanTitle);
    console.log('  Content length:', germanContent.length);
    console.log('  Key insights:', germanKeyInsights.length, 'insights');
    console.log('  Keywords:', germanKeywords.length, 'keywords');

    // Convert HTML content to Lexical format
    console.log('🔄 Converting German content to Lexical format...');
    const htmlResult = await htmlToLexical(germanContent);

    if (!htmlResult.metrics.success) {
      console.error('❌ HTML to Lexical conversion failed:', htmlResult);
      return NextResponse.json(
        {
          success: false,
          error: 'HTML to Lexical conversion failed',
          details: htmlResult.metrics,
        },
        { status: 500 }
      );
    }

    const germanContentLexical = htmlResult.result;

    // Strip HTML formatting from title
    const stripHtml = (text: string): string => {
      return text
        .replace(/<[^>]*>/g, '') // Remove all HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&') // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
    };

    const cleanGermanTitle = stripHtml(germanTitle);
    console.log('🧹 Title cleaning:', germanTitle, '→', cleanGermanTitle);

    // Prepare update data with German translated content
    const updateData: any = {
      hasGermanTranslation: true, // Set flag to show German tab
      germanTab: {
        germanTitle: cleanGermanTitle,
        germanContent: germanContentLexical,
        germanSummary: germanSummary,
        germanKeyInsights: germanKeyInsights.map((insight: string) => ({
          insight,
        })),
        germanKeywords: germanKeywords.map((keyword: string) => ({
          keyword,
        })),
        linguisticAccuracy: translatedData.quality?.linguisticAccuracy || 85,
        culturalAdaptation: translatedData.quality?.culturalAdaptation || 80,
      },
    };

    // Update workflow stage to "translated" if it's currently "candidate-article"
    if (article.workflowStage === 'candidate-article') {
      updateData.workflowStage = 'translated';
    }

    // Update the article
    const updatedArticle = await payload.update({
      collection: 'articles',
      id: articleId,
      data: updateData,
    });

    console.log(
      `✅ German ${isReTranslation ? 're-' : ''}translation completed for article ${articleId}`
    );

    if (isReTranslation) {
      console.log('🔄 Re-translation summary:');
      console.log('  New German title:', cleanGermanTitle);
      console.log('  Content length:', germanContent.length, 'characters');
      console.log(
        '  Processing time:',
        translationResult.metrics.processingTime,
        'ms'
      );
    }

    return NextResponse.json({
      success: true,
      message: `Article ${isReTranslation ? 're-' : ''}translated to German successfully`,
      translatedContent: {
        germanTitle: cleanGermanTitle,
        germanContent: germanContentLexical,
        germanSummary: germanSummary,
        germanKeyInsights: germanKeyInsights,
        germanKeywords: germanKeywords,
      },
      metrics: {
        processingTime: translationResult.metrics.processingTime,
        linguisticAccuracy: translatedData.quality?.linguisticAccuracy || 85,
        culturalAdaptation: translatedData.quality?.culturalAdaptation || 80,
      },
      article: updatedArticle,
    });
  } catch (error: any) {
    console.error('❌ Error translating article:', error);

    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Unknown error occurred during translation',
      },
      { status: 500 }
    );
  }
}
