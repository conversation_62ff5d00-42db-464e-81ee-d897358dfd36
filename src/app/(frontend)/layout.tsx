import type { Metada<PERSON> } from 'next';
import { headers } from 'next/headers';
import { Merriwe<PERSON>, <PERSON><PERSON> } from 'next/font/google';
import './global.css';

import { ThemeProvider } from '@/components/theme-provider';
import HeaderNavigation from '@/components/navigation/header-navigation';

// Configure fonts with Next.js optimization
const merriweather = Merriweather({
  weight: ['300', '400', '700', '900'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-merriweather',
});

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto',
});

export const metadata: Metadata = {
  title: 'Börsen Blick',
  description: 'Deutsche Finanz- und Wirtschaftsnachrichten',
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get current pathname for active navigation state
  const headersList = await headers();
  const pathname = headersList.get('x-pathname') || '/';

  return (
    <html
      lang="de"
      className={`${merriweather.variable} ${roboto.variable}`}
      suppressHydrationWarning
    >
      <body className="font-roboto">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="min-h-screen flex flex-col bg-background">
            <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
              <div className="flex h-16 items-center">
                <HeaderNavigation currentPath={pathname} />
              </div>
            </header>
            <main className="flex-1">{children}</main>
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
