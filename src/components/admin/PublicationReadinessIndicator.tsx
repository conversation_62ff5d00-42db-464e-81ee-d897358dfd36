'use client';

import React from 'react';
import { useFormFields } from '@payloadcms/ui';

interface PublicationReadinessProps {
  path: string;
  name: string;
}

export const PublicationReadinessIndicator: React.FC<
  PublicationReadinessProps
> = () => {
  const featuredImage = useFormFields(
    ([fields]) => fields.featuredImage?.value
  );
  const metaTitle = useFormFields(([fields]) => fields['meta.title']?.value);
  const metaDescription = useFormFields(
    ([fields]) => fields['meta.description']?.value
  );
  const metaImage = useFormFields(([fields]) => fields['meta.image']?.value);
  const workflowStage = useFormFields(
    ([fields]) => fields.workflowStage?.value
  );

  // German content fields
  const hasGermanTranslation = useFormFields(
    ([fields]) => fields.hasGermanTranslation?.value
  );
  const germanTitle = useFormFields(
    ([fields]) => fields['germanTab.germanTitle']?.value
  );
  const germanSummary = useFormFields(
    ([fields]) => fields['germanTab.germanSummary']?.value
  );
  const germanContent = useFormFields(
    ([fields]) => fields['germanTab.germanContent']?.value
  );

  // Categories and placement fields
  const categories = useFormFields(([fields]) => fields.categories?.value);
  const placement = useFormFields(([fields]) => fields.placement?.value);

  // Only show for articles that are in review or published stages
  if (workflowStage !== 'ready-for-review' && workflowStage !== 'published') {
    return null;
  }

  // Validate SEO title with plugin-compatible length requirements
  const validateSEOTitle = () => {
    if (!metaTitle || typeof metaTitle !== 'string' || !metaTitle.trim()) {
      return { passed: false, message: 'SEO title is required' };
    }
    const titleLength = metaTitle.trim().length;
    if (titleLength < 30) {
      return {
        passed: false,
        message: `SEO title too short (${titleLength}/30-60 chars)`,
      };
    }
    if (titleLength > 60) {
      return {
        passed: false,
        message: `SEO title too long (${titleLength}/30-60 chars)`,
      };
    }
    return {
      passed: true,
      message: `SEO title is valid (${titleLength} chars)`,
    };
  };

  // Validate SEO description with plugin-compatible length requirements
  const validateSEODescription = () => {
    if (
      !metaDescription ||
      typeof metaDescription !== 'string' ||
      !metaDescription.trim()
    ) {
      return { passed: false, message: 'SEO description is required' };
    }
    const descLength = metaDescription.trim().length;
    if (descLength < 100) {
      return {
        passed: false,
        message: `SEO description too short (${descLength}/100-160 chars)`,
      };
    }
    if (descLength > 160) {
      return {
        passed: false,
        message: `SEO description too long (${descLength}/100-160 chars)`,
      };
    }
    return {
      passed: true,
      message: `SEO description is valid (${descLength} chars)`,
    };
  };

  // Helper function to check if Lexical content has actual content
  const hasLexicalContent = (lexicalData: any): boolean => {
    if (!lexicalData) return false;

    // Handle both string and object formats
    let content = lexicalData;
    if (typeof lexicalData === 'string') {
      try {
        content = JSON.parse(lexicalData);
      } catch {
        return lexicalData.trim().length > 0;
      }
    }

    // Check if content has root and children
    if (!content.root || !content.root.children) return false;

    // Recursively check if there's actual text content
    const hasTextContent = (node: any): boolean => {
      if (!node) return false;

      // If node has text, check if it's not empty
      if (node.text && node.text.trim().length > 0) {
        return true;
      }

      // If node has children, check them recursively
      if (node.children && Array.isArray(node.children)) {
        return node.children.some(hasTextContent);
      }

      return false;
    };

    return content.root.children.some(hasTextContent);
  };

  // Validate German content
  const validateGermanContent = () => {
    if (!hasGermanTranslation) {
      return {
        passed: false,
        message: 'German translation flag must be enabled',
      };
    }

    if (
      !germanTitle ||
      typeof germanTitle !== 'string' ||
      !germanTitle.trim()
    ) {
      return { passed: false, message: 'German title is required' };
    }

    if (
      !germanSummary ||
      typeof germanSummary !== 'string' ||
      !germanSummary.trim()
    ) {
      return { passed: false, message: 'German summary is required' };
    }

    if (!germanContent) {
      return { passed: false, message: 'German content is required' };
    }

    const hasContent = hasLexicalContent(germanContent);
    if (!hasContent) {
      return { passed: false, message: 'German content appears to be empty' };
    }

    return { passed: true, message: 'German content is complete' };
  };

  // Validate categories
  const validateCategories = () => {
    if (!categories || !Array.isArray(categories) || categories.length === 0) {
      return { passed: false, message: 'At least one category is required' };
    }

    return {
      passed: true,
      message: `${categories.length} categor${categories.length === 1 ? 'y' : 'ies'} assigned`,
    };
  };

  // Validate placement (tier)
  const validatePlacement = () => {
    if (!placement || typeof placement !== 'string' || !placement.trim()) {
      return { passed: false, message: 'Article tier placement is required' };
    }

    const tierLabels = {
      'tier-1': 'Tier 1',
      'tier-2': 'Tier 2',
      'tier-3': 'Tier 3',
    };

    const tierLabel =
      tierLabels[placement as keyof typeof tierLabels] || placement;
    return { passed: true, message: `Placed in ${tierLabel}` };
  };

  const titleValidation = validateSEOTitle();
  const descriptionValidation = validateSEODescription();
  const germanValidation = validateGermanContent();
  const categoriesValidation = validateCategories();
  const placementValidation = validatePlacement();

  const checks = [
    {
      name: 'Featured Image',
      passed: !!featuredImage,
      message: featuredImage
        ? 'Featured image is set'
        : 'Featured image is required',
    },
    {
      name: 'SEO Title',
      passed: titleValidation.passed,
      message: titleValidation.message,
    },
    {
      name: 'SEO Description',
      passed: descriptionValidation.passed,
      message: descriptionValidation.message,
    },
    {
      name: 'SEO Meta Image',
      passed: !!metaImage,
      message: metaImage
        ? 'SEO meta image is set'
        : 'SEO meta image is required',
    },
    {
      name: 'German Content',
      passed: germanValidation.passed,
      message: germanValidation.message,
    },
    {
      name: 'Categories',
      passed: categoriesValidation.passed,
      message: categoriesValidation.message,
    },
    {
      name: 'Placement',
      passed: placementValidation.passed,
      message: placementValidation.message,
    },
  ];

  const allChecksPassed = checks.every(check => check.passed);
  const passedCount = checks.filter(check => check.passed).length;

  return (
    <div
      className="border rounded-md"
      style={{
        backgroundColor: 'var(--theme-elevation-100, #f9f9f9)',
        borderColor: 'var(--theme-elevation-200, #e0e0e0)',
        color: 'var(--theme-text, #333)',
        padding: 'var(--base-half, 0.75rem)',
        marginBottom: 'var(--base, 1rem)',
        borderRadius: 'var(--border-radius-m, 0.375rem)',
      }}
    >
      <div
        className="flex items-center"
        style={{
          marginBottom: 'var(--base-quarter, 0.5rem)',
        }}
      >
        <div
          className="w-3 h-3 rounded-full"
          style={{
            backgroundColor: allChecksPassed ? '#22c55e' : '#ef4444',
            marginRight: 'var(--base-quarter, 0.5rem)',
            width: '12px',
            height: '12px',
          }}
        />
        <strong
          style={{
            color: 'var(--theme-text, #333)',
            fontSize: 'var(--font-size-sm, 0.875rem)',
          }}
        >
          Publication Readiness ({passedCount}/{checks.length})
        </strong>
      </div>

      {allChecksPassed ? (
        <div
          style={{
            color: '#22c55e',
            fontSize: 'var(--font-size-xs, 0.75rem)',
            marginBottom: 'var(--base-quarter, 0.5rem)',
          }}
        >
          ✅ Ready for publication
        </div>
      ) : (
        <div
          style={{
            color: '#ef4444',
            fontSize: 'var(--font-size-xs, 0.75rem)',
            marginBottom: 'var(--base-quarter, 0.5rem)',
          }}
        >
          ⚠️ Publication blocked - complete required items
        </div>
      )}

      <div style={{ fontSize: 'var(--font-size-xs, 0.75rem)' }}>
        {checks.map((check, index) => (
          <div
            key={index}
            className="flex items-center"
            style={{
              marginBottom: 'var(--base-eighth, 0.25rem)',
            }}
          >
            <span
              style={{
                color: check.passed ? '#22c55e' : '#ef4444',
                marginRight: 'var(--base-quarter, 0.5rem)',
              }}
            >
              {check.passed ? '✅' : '❌'}
            </span>
            <span
              style={{
                color: 'var(--theme-text, #333)',
              }}
            >
              {check.name}: {check.message}
            </span>
          </div>
        ))}
      </div>

      {!allChecksPassed && (
        <div
          style={{
            color: 'var(--theme-text-dim, #666)',
            fontSize: 'var(--font-size-xs, 0.75rem)',
            fontStyle: 'italic',
            marginTop: 'var(--base-quarter, 0.5rem)',
          }}
        >
          Use the auto-generate buttons in the Featured Image and SEO sections
          to quickly complete these requirements. Enable German translation and
          complete the German content tab. Assign at least one category and
          select a tier placement.
        </div>
      )}
    </div>
  );
};

export default PublicationReadinessIndicator;
