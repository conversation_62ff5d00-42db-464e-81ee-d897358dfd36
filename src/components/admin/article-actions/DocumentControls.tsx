/**
 * Article Document Controls Component
 *
 * Provides translation functionality for candidate articles with proper UI updates.
 *
 * Key Features:
 * - Translates enhanced English content to German using OpenAI
 * - Updates form fields programmatically using PayloadCMS patterns
 * - Forces page refresh to ensure UI reflects changes immediately
 * - Provides visual feedback during translation process
 * - <PERSON>les re-translation scenarios
 *
 * Solution for UI Update Issue:
 * - Uses dispatchFields() to update form state
 * - Implements router.refresh() to force page re-render
 * - Provides immediate visual feedback with button state changes
 * - Includes fallback option for window.location.reload() if needed
 *
 * <AUTHOR> Blick Development Team
 * @updated 2025-01-16 - Fixed UI refresh issue after translation
 */
'use client';

import { useState, useCallback } from 'react';
import { useDocumentInfo, useAllFormFields } from '@payloadcms/ui';
import { useToast } from '@/hooks/use-toast';
import { ToastProvider } from '@/components/admin/toast-provider';
import { reduceFieldsToValues } from 'payload/shared';
import { useRouter } from 'next/navigation';

export const ArticleDocumentControls = () => {
  const { id } = useDocumentInfo();
  const [fields, dispatchFields] = useAllFormFields();
  const { toast } = useToast();
  const router = useRouter();
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationJustCompleted, setTranslationJustCompleted] =
    useState(false);

  // Convert fields to data using PayloadCMS helper
  const data = reduceFieldsToValues(fields, true);
  const workflowStage = data?.workflowStage;
  const articleType = data?.articleType;
  const hasEnhancedEnglish =
    data?.englishTab?.enhancedTitle && data?.englishTab?.enhancedContent;
  const hasGermanTranslation =
    data?.germanTab?.germanTitle && data?.germanTab?.germanContent;

  // Translation button should be available when:
  // 1. Article has enhanced English content
  // 2. Article is in appropriate workflow stage for translation
  const canTranslate =
    hasEnhancedEnglish &&
    [
      'candidate-article',
      'translated',
      'ready-for-review',
      'published',
    ].includes(workflowStage);

  const showTranslationButton = canTranslate;

  // Define the translation handler (must be before any conditional returns)
  const handleTranslateToGerman = useCallback(async () => {
    if (!id) {
      toast.error('No article ID found');
      return;
    }

    if (!hasEnhancedEnglish) {
      toast.error(
        'Article must have enhanced English content before translation'
      );
      return;
    }

    setIsTranslating(true);

    try {
      const response = await fetch('/api/articles/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ articleId: id }),
      });

      const result = await response.json();

      if (result.success && result.translatedContent) {
        console.log('✅ Translation successful');
        console.log('Translated content:', result.translatedContent);

        // Show success feedback to user
        toast.success('German translation completed successfully!', {
          description: hasGermanTranslation
            ? 'Article has been re-translated to German.'
            : 'Article has been translated to German.',
          duration: 3000,
        });

        // Update form fields using PayloadCMS native patterns
        // The API response contains the translated content
        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanTitle',
          value: result.translatedContent.germanTitle,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanContent',
          value: result.translatedContent.germanContent,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanSummary',
          value: result.translatedContent.germanSummary,
        });

        // Update additional fields if they exist in the response
        if (result.translatedContent.germanKeyInsights) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeyInsights',
            value: result.translatedContent.germanKeyInsights,
          });
        }

        if (result.translatedContent.germanKeywords) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeywords',
            value: result.translatedContent.germanKeywords,
          });
        }

        dispatchFields({
          type: 'UPDATE',
          path: 'hasGermanTranslation',
          value: true,
        });

        // Update workflow stage to 'translated'
        dispatchFields({
          type: 'UPDATE',
          path: 'workflowStage',
          value: 'translated',
        });

        console.log('✅ Form fields updated successfully');

        // Set immediate visual feedback
        setTranslationJustCompleted(true);

        // Log the current form state for debugging
        console.log('📊 Current form state after field updates:', {
          germanTitle:
            result.translatedContent.germanTitle?.substring(0, 50) + '...',
          workflowStage: 'translated',
          hasGermanTranslation: true,
          fieldsUpdated: Object.keys(result.translatedContent).length,
        });

        // Force form re-render and page refresh using PayloadCMS best practices
        // This ensures the UI reflects the updated state immediately
        setTimeout(() => {
          // Method 1: Use router.refresh() for Next.js App Router (preferred)
          router.refresh();

          // Method 2: Alternative fallback - uncomment if router.refresh() doesn't work
          // This will force a full page reload to ensure all content is updated
          // window.location.reload();
        }, 1500); // Delay to allow toast to show and field updates to process

        // Reset the completion flag after refresh
        setTimeout(() => {
          setTranslationJustCompleted(false);
        }, 2000);
      } else {
        console.error('❌ Translation failed:', result.error);
        toast.error('Translation failed', {
          description:
            result.error || 'An unknown error occurred during translation',
        });
      }
    } catch (error) {
      console.error('❌ Translation error:', error);
      toast.error('Translation failed', {
        description: 'Failed to communicate with the translation service',
      });
    } finally {
      setIsTranslating(false);
    }
  }, [
    id,
    hasEnhancedEnglish,
    hasGermanTranslation,
    dispatchFields,
    toast,
    router,
  ]);

  // Only show for generated articles with appropriate workflow stage
  if (
    articleType !== 'generated' ||
    ![
      'candidate-article',
      'translated',
      'ready-for-review',
      'published',
    ].includes(workflowStage)
  ) {
    return null;
  }

  // Determine button states
  const isTranslationDisabled =
    !canTranslate || isTranslating || translationJustCompleted;

  const getTranslationButtonText = () => {
    if (isTranslating) return 'Translating to German...';
    if (translationJustCompleted) return 'Translation Complete! Refreshing...';
    if (hasGermanTranslation) return 'Re-Translate to German';
    return 'Translate to German';
  };

  const getTranslationButtonColor = () => {
    if (isTranslating) return '#6B7280'; // Gray for loading
    if (translationJustCompleted) return '#10B981'; // Bright green for success
    if (hasGermanTranslation) return '#059669'; // Green for re-translation
    return '#2563EB'; // Blue for first translation
  };

  return (
    <ToastProvider>
      <div
        className="flex gap-2 items-center mb-4"
        style={{
          marginBottom: 'var(--base, 1rem)',
        }}
      >
        {/* Translation Button - Only show when conditions are met */}
        {showTranslationButton && (
          <button
            onClick={handleTranslateToGerman}
            disabled={isTranslationDisabled}
            className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-colors"
            style={{
              backgroundColor: getTranslationButtonColor(),
              color: 'white',
              border: 'none',
              cursor: isTranslationDisabled ? 'not-allowed' : 'pointer',
              opacity: isTranslationDisabled ? 0.6 : 1,
              fontSize: 'var(--font-size-sm, 0.875rem)',
              padding:
                'var(--base-quarter, 0.5rem) var(--base-three-quarters, 0.75rem)',
              borderRadius: 'var(--border-radius-m, 0.375rem)',
              minHeight: '32px',
            }}
          >
            {isTranslating && (
              <div
                style={{
                  width: '14px',
                  height: '14px',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                  flexShrink: 0,
                }}
              />
            )}
            {translationJustCompleted && (
              <div
                style={{
                  width: '14px',
                  height: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                }}
              >
                ✓
              </div>
            )}
            <span style={{ lineHeight: '1' }}>
              {getTranslationButtonText()}
            </span>
          </button>
        )}
      </div>

      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </ToastProvider>
  );
};

export default ArticleDocumentControls;
