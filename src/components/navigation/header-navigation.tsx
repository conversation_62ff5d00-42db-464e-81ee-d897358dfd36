import { NavigationClient } from './navigation-client';
import { resolveNavigationLinks, getFallbackNavigation } from './link-resolver';
import { getPayload } from 'payload';
import config from '@payload-config';
import type { Header } from '../../../payload-types';

interface HeaderNavigationProps {
  currentPath?: string;
}

export default async function HeaderNavigation({
  currentPath,
}: HeaderNavigationProps) {
  try {
    // PayloadCMS native pattern - direct Local API usage
    const payload = await getPayload({ config });

    const header: Header = await payload.findGlobal({
      slug: 'header',
      depth: 1,
    });

    // Work naturally with PayloadCMS data structure
    if (header?.navItems?.length) {
      const items = resolveNavigationLinks(header.navItems);
      return <NavigationClient items={items} currentPath={currentPath} />;
    }

    // Graceful fallback using framework patterns
    const fallbackItems = getFallbackNavigation();
    return <NavigationClient items={fallbackItems} currentPath={currentPath} />;
  } catch (error) {
    // Framework-appropriate error handling
    console.error('Navigation: Failed to fetch header global:', error);
    const fallbackItems = getFallbackNavigation();
    return <NavigationClient items={fallbackItems} currentPath={currentPath} />;
  }
}
