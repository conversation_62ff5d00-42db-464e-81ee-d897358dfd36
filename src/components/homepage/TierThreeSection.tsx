import { getCachedTier3Articles } from '@/lib/cache/articles';
import TierSection from './TierSection';
import { DEFAULT_HOMEPAGE_CONFIG } from './types';

export default async function TierThreeSection() {
  const config = DEFAULT_HOMEPAGE_CONFIG;
  const tier3Data = await getCachedTier3Articles();

  return (
    <TierSection
      tier="tier-3"
      articles={tier3Data.docs}
      title="Aktuelle Meldungen"
      variant="title-only"
      locale={config.locale}
      maxArticles={config.tiers.find(t => t.tier === 'tier-3')?.maxArticles}
      emptyStateMessage="Derzeit keine aktuellen Meldungen verfügbar"
      showMoreButton={true}
      showDescriptionForFirst={999}
      className="space-y-2 sm:space-y-2.5"
    />
  );
}
