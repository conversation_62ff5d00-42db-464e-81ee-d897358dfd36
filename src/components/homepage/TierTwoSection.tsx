import { getCachedTier2Articles } from '@/lib/cache/articles';
import TierSection from './TierSection';
import { DEFAULT_HOMEPAGE_CONFIG } from './types';

export default async function TierTwoSection() {
  const config = DEFAULT_HOMEPAGE_CONFIG;
  const tier2Data = await getCachedTier2Articles();

  return (
    <div id="market-analysis">
      <TierSection
        tier="tier-2"
        articles={tier2Data.docs}
        title="Marktanalyse"
        variant="horizontal"
        locale={config.locale}
        maxArticles={config.tiers.find(t => t.tier === 'tier-2')?.maxArticles}
        emptyStateMessage="Keine Marktanalyse verfügbar"
        showMoreButton={true}
        className="space-y-2 sm:space-y-3"
      />
    </div>
  );
}
