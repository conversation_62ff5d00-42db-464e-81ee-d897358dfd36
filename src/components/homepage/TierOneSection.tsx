import { getCachedTier1Articles } from '@/lib/cache/articles';
import TierSection from './TierSection';
import { DEFAULT_HOMEPAGE_CONFIG } from './types';

export default async function TierOneSection() {
  const config = DEFAULT_HOMEPAGE_CONFIG;
  const tier1Data = await getCachedTier1Articles();

  return (
    <TierSection
      tier="tier-1"
      articles={tier1Data.docs}
      title="Wichtige Artikel"
      variant="featured"
      locale={config.locale}
      maxArticles={config.tiers.find(t => t.tier === 'tier-1')?.maxArticles}
      emptyStateMessage="Keine wichtigen Artikel verfügbar"
      showMoreButton={false}
      className="mb-4 sm:mb-5 lg:mb-6"
    />
  );
}
