import { unstable_cache } from 'next/cache';
import { getPayload } from 'payload';
import config from '@/payload.config';
import type { Article } from '@/payload-types';
import type { PaginatedDocs } from 'payload';

// Type definitions for better type safety
export interface TierArticles extends PaginatedDocs<Article> {}

export interface CachedTierData {
  tier1: TierArticles;
  tier2: TierArticles;
  tier3: TierArticles;
}

// Individual tier fetching functions
const fetchTierArticles = async (
  tier: 'tier-1' | 'tier-2' | 'tier-3',
  limit: number
): Promise<TierArticles> => {
  const payload = await getPayload({ config });

  const sortConfig = {
    'tier-1': ['-pinned', '-publishedAt'],
    'tier-2': ['-publishedAt'],
    'tier-3': ['-publishedAt'],
  };

  return await payload.find({
    collection: 'articles',
    where: {
      and: [
        { placement: { equals: tier } },
        { workflowStage: { equals: 'published' } },
      ],
    },
    sort: sortConfig[tier],
    limit,
    depth: 2,
  });
};

// Cached tier fetching functions with proper Next.js caching
export const getCachedTier1Articles = unstable_cache(
  async () => fetchTierArticles('tier-1', 3),
  ['tier-1-articles'],
  {
    revalidate: 300, // 5 minutes
    tags: ['articles', 'tier-1'],
  }
);

export const getCachedTier2Articles = unstable_cache(
  async () => fetchTierArticles('tier-2', 6),
  ['tier-2-articles'],
  {
    revalidate: 300, // 5 minutes
    tags: ['articles', 'tier-2'],
  }
);

export const getCachedTier3Articles = unstable_cache(
  async () => fetchTierArticles('tier-3', 8),
  ['tier-3-articles'],
  {
    revalidate: 300, // 5 minutes
    tags: ['articles', 'tier-3'],
  }
);

// Combined cached data fetching
export const getCachedAllTierArticles = unstable_cache(
  async (): Promise<CachedTierData> => {
    const [tier1, tier2, tier3] = await Promise.all([
      fetchTierArticles('tier-1', 3),
      fetchTierArticles('tier-2', 6),
      fetchTierArticles('tier-3', 8),
    ]);

    return { tier1, tier2, tier3 };
  },
  ['all-tier-articles'],
  {
    revalidate: 300, // 5 minutes
    tags: ['articles'],
  }
);
