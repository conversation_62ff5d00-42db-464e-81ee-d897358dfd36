/**
 * German Translation API
 * OpenAI-powered translation service for converting enhanced English content
 * to literal German translations suitable for German financial markets
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-07-09
 * @description Provides literal German translations (not creative adaptations)
 */

import OpenAI from 'openai';
import { zodTextFormat } from 'openai/helpers/zod';
import { z } from 'zod';
import {
  cleanContent,
  cleanTitle,
  getCharacterReport,
} from '@/lib/utils/character-cleaning';

// Initialize OpenAI client with error handling
let openai: OpenAI | null = null;

try {
  if (process.env.OPENAI_API_KEY) {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    console.log('✅ OpenAI German Translation client initialized');
  } else {
    console.warn(
      '⚠️ OPENAI_API_KEY not found - German Translation will not function'
    );
  }
} catch (error) {
  console.error(
    '❌ Failed to initialize OpenAI client for German translation:',
    error
  );
}

/**
 * German Translation Schema - Structured output for literal German translations
 */
export const GermanTranslationSchema = z.object({
  germanTitle: z
    .string()
    .describe(
      'Literal German translation of the English title (50-60 characters)'
    ),
  germanSummary: z
    .string()
    .describe(
      'Literal German translation of the English summary (100-150 characters)'
    ),
  germanContent: z
    .string()
    .describe(
      'Literal German translation of the enhanced English content. CRITICAL: Preserve EXACT HTML structure - only translate text content inside HTML tags, never modify the HTML markup, tags, or attributes'
    ),
  germanKeyInsights: z
    .array(z.string())
    .describe('Literal German translations of the English key insights'),
  germanKeywords: z
    .array(z.string())
    .describe(
      'German translations of keywords (only those with German equivalents)'
    ),
  quality: z
    .object({
      linguisticAccuracy: z
        .number()
        .min(0)
        .max(100)
        .describe('Linguistic accuracy score (0-100)'),
      culturalAdaptation: z
        .number()
        .min(0)
        .max(100)
        .describe('Cultural adaptation for German market (0-100)'),
      preservationScore: z
        .number()
        .min(0)
        .max(100)
        .describe('How well original meaning AND HTML structure were preserved exactly (0-100)'),
    })
    .describe('Translation quality metrics'),
  processing: z
    .object({
      translationType: z
        .literal('literal')
        .describe('Type of translation performed'),
      sourceLanguage: z.literal('english').describe('Source language'),
      targetLanguage: z.literal('german').describe('Target language'),
      wordsTranslated: z.number().describe('Number of words translated'),
    })
    .describe('Processing metadata'),
});

export type GermanTranslationResult = z.infer<typeof GermanTranslationSchema>;

/**
 * Input structure for German translation
 */
export interface TranslationInput {
  title: string;
  summary: string;
  content: string;
  keyInsights: string[];
  keywords: string[];
}

/**
 * Translation options
 */
export interface TranslationOptions {
  temperature?: number;
  includeProcessingMetadata?: boolean;
  timeout?: number;
}

/**
 * Translation result with metrics
 */
export interface TranslationApiResult {
  success: boolean;
  data?: GermanTranslationResult;
  error?: string;
  validation: {
    isValid: boolean;
    issues: string[];
    qualityScore: number;
  };
  metrics: {
    processingTime: number;
    tokenUsage: number;
    costEstimate: number;
  };
  characterCleaning?: {
    hasIssues: boolean;
    issues: string[];
    cleanedLength: number;
    originalLength: number;
  };
}

/**
 * Generate German translation prompt
 */
function formatGermanTranslationPrompt(input: TranslationInput): {
  systemPrompt: string;
  userPrompt: string;
} {
  const systemPrompt = `You are a professional German financial translator specializing in literal, accurate translations of English financial content into German.

## TRANSLATION REQUIREMENTS

**Translation Type**: LITERAL/EXACT translation (NOT creative adaptation)
**Target Audience**: German financial market readers
**Content Type**: Enhanced English financial news article
**Output Format**: Structured German translation with quality metrics

## CRITICAL GUIDELINES - STRUCTURE PRESERVATION

1. **EXACT STRUCTURE PRESERVATION**: Do NOT change any HTML markup, tags, or formatting
2. **HTML MARKUP RULE**: Keep ALL HTML tags exactly as they are - only translate the text content inside tags
3. **NO STRUCTURAL CHANGES**: Do not add, remove, or modify any HTML elements, attributes, or structure
4. **PARAGRAPH PRESERVATION**: Maintain exact paragraph breaks, line breaks, and spacing
5. **FORMATTING PRESERVATION**: Keep all bold, italic, links, lists, and other formatting exactly as provided

## TRANSLATION GUIDELINES

1. **Literal Translation**: Provide exact, word-for-word German equivalents wherever possible
2. **Financial Terminology**: Use standard German financial terms (Aktien, Börse, Unternehmen, etc.)
3. **Accuracy Priority**: Prioritize accuracy over stylistic adaptation
4. **No Creative Changes**: Do not add, remove, or significantly modify content meaning
5. **German Market Context**: Use German stock exchanges (DAX, MDAX) and European financial references where appropriate

## STRICT HTML RULES

- If you see `<p>English text</p>` → return `<p>German text</p>`
- If you see `<strong>English text</strong>` → return `<strong>German text</strong>`
- If you see `<a href="url">English text</a>` → return `<a href="url">German text</a>`
- NEVER change the HTML structure, only translate the text content within tags
- NEVER add new HTML tags or remove existing ones
- NEVER modify HTML attributes, URLs, or tag properties

## TRANSLATION APPROACH

- **Title**: Literal translation maintaining key financial terms
- **Summary**: Exact translation preserving all key information
- **Content**: Translate ONLY the text content, preserve ALL HTML markup exactly
- **Key Insights**: Direct translations maintaining analytical accuracy
- **Keywords**: Only translate keywords that have clear German equivalents

## QUALITY METRICS

- **Linguistic Accuracy**: Grammar, syntax, and word choice correctness
- **Cultural Adaptation**: Appropriate use of German financial market terminology
- **Preservation Score**: How well original meaning and HTML structure were maintained

## OUTPUT REQUIREMENTS

- Return structured German translation with quality assessment
- Include processing metadata for tracking
- Maintain EXACT HTML formatting in content translation
- Provide translation quality scores for review`;

  const userPrompt = `Please translate the following enhanced English financial article content into literal German:

## ENGLISH CONTENT TO TRANSLATE

**Title**: ${input.title}

**Summary**: ${input.summary}

**Content**: ${input.content}

**Key Insights**:
${input.keyInsights.map((insight, index) => `${index + 1}. ${insight}`).join('\n')}

**Keywords**: ${input.keywords.join(', ')}

## CRITICAL TRANSLATION INSTRUCTIONS

1. **Title**: Translate literally into German (50-60 characters) - plain text only
2. **Summary**: Translate literally into German (100-150 characters) - plain text only
3. **Content**: CRITICAL - Preserve EXACT HTML structure:
   - Keep ALL HTML tags exactly as they appear
   - Only translate the text content INSIDE the HTML tags
   - Do NOT add, remove, or modify any HTML markup
   - Do NOT change tag attributes, URLs, or structure
   - Example: `<p>English text</p>` becomes `<p>German text</p>`
   - Example: `<strong>Bold English</strong>` becomes `<strong>Bold German</strong>`
4. **Key Insights**: Translate each insight literally into German (plain text)
5. **Keywords**: Only translate keywords that have clear German equivalents
6. **Quality Assessment**: Assess translation quality using the provided metrics
7. **NO CREATIVE CHANGES**: Do not add interpretations or additional context

## STRUCTURE PRESERVATION EXAMPLES

✅ CORRECT:
- Input: `<p>The stock market rose today.</p>`
- Output: `<p>Der Aktienmarkt stieg heute.</p>`

❌ WRONG:
- Input: `<p>The stock market rose today.</p>`
- Output: `<div><p>Der Aktienmarkt stieg heute.</p></div>` (added div)
- Output: `<p><strong>Der Aktienmarkt stieg heute.</strong></p>` (added strong)
- Output: `Der Aktienmarkt stieg heute.` (removed p tag)

Return the structured German translation with quality assessment.`;

  return { systemPrompt, userPrompt };
}

/**
 * Validate German translation output
 */
function validateGermanTranslation(result: GermanTranslationResult): {
  isValid: boolean;
  issues: string[];
  qualityScore: number;
} {
  const issues: string[] = [];
  let qualityScore = 100;

  // Title validation
  if (result.germanTitle.length < 30 || result.germanTitle.length > 80) {
    issues.push('German title length out of optimal range (30-80 chars)');
    qualityScore -= 10;
  }

  // Summary validation
  if (result.germanSummary.length < 50 || result.germanSummary.length > 200) {
    issues.push('German summary length out of range (50-200 chars)');
    qualityScore -= 10;
  }

  // Content validation
  if (result.germanContent.length < 200) {
    issues.push('German content too short');
    qualityScore -= 20;
  }

  // Key insights validation
  if (result.germanKeyInsights.length === 0) {
    issues.push('No German key insights provided');
    qualityScore -= 15;
  }

  // Quality scores validation
  if (result.quality.linguisticAccuracy < 70) {
    issues.push('Linguistic accuracy below threshold (70%)');
    qualityScore -= 15;
  }

  if (result.quality.preservationScore < 80) {
    issues.push('Original meaning preservation below threshold (80%)');
    qualityScore -= 10;
  }

  return {
    isValid: issues.length === 0,
    issues,
    qualityScore: Math.max(0, qualityScore),
  };
}

/**
 * Apply character cleaning to German translation
 */
function cleanGermanTranslation(
  result: GermanTranslationResult
): GermanTranslationResult {
  return {
    ...result,
    germanTitle: cleanTitle(result.germanTitle),
    germanSummary: cleanContent(result.germanSummary),
    germanContent: cleanContent(result.germanContent),
    germanKeyInsights: result.germanKeyInsights.map(insight =>
      cleanContent(insight)
    ),
    germanKeywords: result.germanKeywords.map(keyword => cleanContent(keyword)),
  };
}

/**
 * Main German Translation Function
 * Translates enhanced English content to literal German translations
 *
 * @param input - English content to translate
 * @param options - Translation configuration options
 * @returns German translation result with metrics
 */
export async function translateToGerman(
  input: TranslationInput,
  options: TranslationOptions = {}
): Promise<TranslationApiResult> {
  const startTime = Date.now();

  if (!openai) {
    return {
      success: false,
      error: 'OpenAI client not initialized - check OPENAI_API_KEY',
      validation: {
        isValid: false,
        issues: ['OpenAI client not available'],
        qualityScore: 0,
      },
      metrics: {
        processingTime: Date.now() - startTime,
        tokenUsage: 0,
        costEstimate: 0,
      },
    };
  }

  try {
    // Validate input
    if (!input.content || input.content.length < 100) {
      throw new Error(
        'Content too short for translation (minimum 100 characters)'
      );
    }

    if (!input.title || input.title.length < 5) {
      throw new Error('Title too short for translation (minimum 5 characters)');
    }

    console.log('🇩🇪 Starting German translation...');
    console.log(
      `📄 Processing: "${input.title}" (${input.content.length} chars)`
    );

    // Prepare translation prompt
    const { systemPrompt, userPrompt } = formatGermanTranslationPrompt(input);

    // Make structured API call for German translation
    const response = await openai.responses.parse({
      model: 'gpt-4o-2024-08-06',
      input: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt },
      ],
      text: {
        format: zodTextFormat(GermanTranslationSchema, 'german_translation'),
      },
      max_output_tokens: 3000,
      temperature: options.temperature || 0.3, // Lower temperature for literal translations
    });

    if (!response.output_parsed) {
      throw new Error('Failed to parse German translation response');
    }

    const result = response.output_parsed;

    // Apply character cleaning to German content
    const cleanedResult: GermanTranslationResult =
      cleanGermanTranslation(result);

    // Generate character cleaning report
    const originalText = JSON.stringify(result);
    const characterReport = getCharacterReport(originalText);

    // Validate German translation output
    const validation = validateGermanTranslation(cleanedResult);

    // Extract processing metadata
    const processingTime = Date.now() - startTime;
    const tokenUsage = response.usage?.total_tokens || 0;
    const costEstimate = tokenUsage * 0.00002; // GPT-4o pricing

    console.log('✅ German translation completed successfully');
    console.log(`📊 Performance: ${processingTime}ms, ${tokenUsage} tokens`);
    console.log(`💰 Cost estimate: $${costEstimate.toFixed(4)}`);
    console.log(
      `🎯 Translation accuracy: ${cleanedResult.quality.linguisticAccuracy}%`
    );
    console.log(
      `📝 Linguistic accuracy: ${cleanedResult.quality.linguisticAccuracy}%`
    );

    return {
      success: true,
      data: cleanedResult,
      validation,
      metrics: {
        processingTime,
        tokenUsage,
        costEstimate,
      },
      characterCleaning: characterReport.hasIssues
        ? characterReport
        : undefined,
    };
  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('❌ German translation failed:', error);

    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error in German translation',
      validation: {
        isValid: false,
        issues: [error instanceof Error ? error.message : 'Unknown error'],
        qualityScore: 0,
      },
      metrics: {
        processingTime,
        tokenUsage: 0,
        costEstimate: 0,
      },
    };
  }
}

/**
 * German Translation Health Check
 */
export async function germanTranslationHealthCheck(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: Record<string, unknown>;
}> {
  try {
    if (!openai) {
      return {
        status: 'unhealthy',
        details: { error: 'OpenAI client not initialized' },
      };
    }

    // Test with minimal input
    const testResult = await translateToGerman(
      {
        title: 'Test Financial Article',
        summary: 'This is a test summary for health check purposes.',
        content:
          'This is test content for the German translation health check. It contains financial terms like stocks, markets, and investments.',
        keyInsights: ['Test insight about financial markets'],
        keywords: ['test', 'financial', 'markets'],
      },
      {
        temperature: 0.3,
      }
    );

    if (testResult.success && testResult.data) {
      return {
        status: 'healthy',
        details: {
          lastCheck: new Date().toISOString(),
          testLinguisticAccuracy: testResult.data.quality.linguisticAccuracy,
          processingTime: testResult.metrics.processingTime,
        },
      };
    } else {
      return {
        status: 'degraded',
        details: {
          error: testResult.error,
          lastCheck: new Date().toISOString(),
        },
      };
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        lastCheck: new Date().toISOString(),
      },
    };
  }
}
