import type { GlobalConfig } from 'payload';
import { revalidateTag } from 'next/cache';
import { link } from '@/fields/link';

export const Header: GlobalConfig = {
  slug: 'header',
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'navItems',
      type: 'array',
      fields: [
        link({
          appearances: false,
        }),
      ],
      maxRows: 6,
      admin: {
        initCollapsed: true,
        components: {
          RowLabel: '@/components/admin/header/RowLabel#RowLabel',
        },
      },
    },
  ],
  hooks: {
    afterChange: [
      ({ doc, req: { payload, context } }) => {
        if (!context?.disableRevalidate) {
          payload.logger.info('Revalidating header');
          revalidateTag('global_header');
        }
        return doc;
      },
    ],
  },
};
