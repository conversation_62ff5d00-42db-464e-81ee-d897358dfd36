# Article Publication Validation - Implementation Plan

## Overview
Implement validation checks to ensure articles meet quality standards before publication. This includes featured image requirements and SEO plugin validation for title, description, and meta image.

## Current State Analysis

### Existing Article Structure
- **Collection**: `Articles` in `src/collections/Articles.ts`
- **Workflow Stages**: `candidate-article` → `translated` → `ready-for-review` → `published`
- **SEO Integration**: Already has SEO plugin with `MetaTitleField`, `MetaDescriptionField`, `MetaImageField`
- **Featured Image**: `featuredImage` field with `upload` type, relation to `media`

### Current Validation Gaps
1. No validation for `featuredImage` before publication
2. No validation for SEO fields before publication  
3. No integration with SEO plugin's built-in validation checks
4. Users can currently publish articles without meeting quality standards

## Implementation Strategy

### Phase 1: Assessment and Safe Implementation Approach

#### 1.1 Database Safety Measures
- **No Schema Changes**: Work with existing fields to avoid data loss
- **Validation Only**: Add validation hooks, don't modify existing data
- **Gradual Rollout**: Implement validation without affecting existing published articles
- **Fallback Handling**: Ensure existing articles remain accessible

#### 1.2 Validation Hook Strategy
- Use PayloadCMS `beforeChange` hooks for validation
- Target only `workflowStage` changes to `published`
- Provide clear error messages for missing requirements
- Allow bypass for existing published articles (grandfather clause)

### Phase 2: Technical Implementation

#### 2.1 Featured Image Validation
```typescript
// In Articles collection beforeChange hook
if (data.workflowStage === 'published' && !data.featuredImage) {
  throw new Error('Featured image is required before publishing')
}
```

#### 2.2 SEO Plugin Integration
- Leverage existing SEO plugin validation
- Check for `meta.title`, `meta.description`, `meta.image` completeness
- Integrate with plugin's built-in validation checks
- Provide user-friendly error messages

#### 2.3 Validation Hook Implementation
```typescript
// Validation logic in beforeChange hook
const validatePublicationRequirements = async (data, operation) => {
  if (data.workflowStage === 'published') {
    // Check featured image
    if (!data.featuredImage) {
      throw new ValidationError('Featured image is required before publishing')
    }
    
    // Check SEO fields
    if (!data.meta?.title || !data.meta?.description || !data.meta?.image) {
      throw new ValidationError('All SEO fields (title, description, image) must be completed before publishing')
    }
    
    // Additional SEO validation using plugin's checks
    await validateSEORequirements(data.meta)
  }
}
```

### Phase 3: User Experience Enhancements

#### 3.1 Admin Interface Improvements
- **Visual Indicators**: Show validation status in admin interface
- **Auto-Generation Links**: Ensure SEO auto-generation buttons are prominent
- **Progress Indicators**: Show checklist of requirements before publishing
- **Helpful Messages**: Clear guidance on what's missing

#### 3.2 Validation Feedback
- **Real-time Validation**: Show validation errors as user types
- **Field-specific Errors**: Highlight exactly which fields need attention
- **Success Indicators**: Green checkmarks when requirements are met
- **Guided Flow**: Step-by-step guidance to complete requirements

### Phase 4: Testing Strategy

#### 4.1 Existing Content Safety
- **Backward Compatibility**: Ensure existing published articles remain accessible
- **Migration Test**: Verify no existing content is broken
- **Validation Bypass**: Test grandfather clause for existing published articles

#### 4.2 New Content Validation
- **Positive Testing**: Verify validation works for new articles
- **Negative Testing**: Confirm articles can't be published without requirements
- **Edge Cases**: Test partial completions and validation edge cases

#### 4.3 User Experience Testing
- **Admin Workflow**: Test complete publication workflow
- **Error Handling**: Verify helpful error messages
- **Auto-generation**: Test SEO field auto-generation functionality

## Implementation Steps

### Step 1: Backup and Preparation
1. **Database Backup**: Create full database backup before any changes
2. **Code Backup**: Commit current working state to version control
3. **Environment Setup**: Test in development environment first

### Step 2: Core Validation Implementation
1. **Add Validation Hooks**: Implement `beforeChange` hooks in Articles collection
2. **Featured Image Check**: Add featured image requirement validation
3. **SEO Plugin Integration**: Integrate with existing SEO plugin validation
4. **Error Handling**: Implement user-friendly error messages

### Step 3: Admin Interface Enhancements
1. **Validation Status**: Add visual indicators for validation status
2. **Requirement Checklist**: Show what's needed before publishing
3. **Auto-generation Prominence**: Make SEO auto-generation more visible
4. **Workflow Guidance**: Add helpful text and guidance

### Step 4: Testing and Refinement
1. **Existing Content**: Verify all existing articles still work
2. **New Article Flow**: Test complete new article publication flow
3. **Error Scenarios**: Test various validation failure scenarios
4. **User Experience**: Refine based on usability testing

### Step 5: Documentation and Training
1. **User Documentation**: Create guide for using new validation system
2. **Admin Training**: Document new publication workflow
3. **Troubleshooting**: Create guide for common validation issues

## Risk Mitigation

### Data Safety Risks
- **Risk**: Existing published articles become inaccessible
- **Mitigation**: Grandfather clause for existing published articles
- **Testing**: Comprehensive testing of existing content

### User Experience Risks
- **Risk**: Validation blocks legitimate publishing
- **Mitigation**: Clear error messages and guidance
- **Testing**: Thorough UX testing of validation workflow

### Technical Risks
- **Risk**: Validation hooks interfere with existing functionality
- **Mitigation**: Careful hook implementation and testing
- **Testing**: Comprehensive regression testing

## Success Criteria

### Functional Requirements
- [x] Featured image required before publishing
- [x] SEO title validation before publishing
- [x] SEO description validation before publishing
- [x] SEO meta image validation before publishing
- [x] Existing published articles remain accessible
- [x] Clear error messages for missing requirements

### User Experience Requirements
- [x] Visual indicators for validation status
- [x] Easy access to auto-generation features
- [x] Helpful guidance throughout publication process
- [x] No disruption to existing workflow for compliant articles

### Technical Requirements
- [x] No data loss or corruption
- [x] Backward compatibility maintained
- [x] Performance impact minimized
- [x] Error handling robust and user-friendly

## Rollback Plan

### Emergency Rollback
1. **Code Rollback**: Revert to previous version of Articles collection
2. **Database Restore**: Restore from backup if needed (unlikely)
3. **User Notification**: Inform users of temporary validation suspension

### Gradual Rollback
1. **Disable Validation**: Add feature flag to disable validation
2. **Fix Issues**: Address any problems while validation is disabled
3. **Re-enable**: Turn validation back on after fixes

## Timeline

### Week 1: Planning and Preparation
- Database backup and environment setup
- Detailed technical design
- Code review and approval

### Week 2: Core Implementation
- Validation hook implementation
- Featured image and SEO checks
- Basic error handling

### Week 3: User Experience
- Admin interface improvements
- Validation status indicators
- Auto-generation enhancements

### Week 4: Testing and Refinement
- Comprehensive testing
- Bug fixes and refinements
- Documentation creation

### Week 5: Deployment and Monitoring
- Production deployment
- User training and documentation
- Monitoring and support

## Next Steps

1. **Review and Approve Plan**: Team review of this implementation plan
2. **Technical Deep Dive**: Detailed technical design for validation hooks
3. **UI/UX Design**: Design validation status indicators and user guidance
4. **Implementation Start**: Begin with Step 1 (Backup and Preparation)

---

**Note**: This plan prioritizes data safety and user experience while ensuring all publication quality requirements are met. The implementation will be gradual and thoroughly tested to minimize risks.

---

# Frontend Style Improvements - Implementation Plan

## Overview
Implement visual improvements to the homepage grid, news cards, and navigation to enhance spacing, alignment, and visual hierarchy.

## Requested Changes

### 1. Grid and Card Spacing
- **Issue**: Current spacing between columns and cards is too large
- **Solution**: Reduce spacing by 50% across all grid areas
- **Impact**: Tighter, more compact layout that shows more content

### 2. News Card Typography
- **Issue**: Too much space between title and description
- **Solution**: Reduce margin between title and description elements
- **Impact**: Better visual hierarchy and content density

### 3. Trending Badge Removal
- **Issue**: Trending badges are currently visible on cards
- **Solution**: Set trending prop to false for all cards
- **Impact**: Cleaner card appearance whilst maintaining functionality

### 4. "View More" Link Alignment
- **Issue**: "View more" links are centre-aligned
- **Solution**: Change alignment to left-aligned
- **Impact**: Better visual flow and consistency

### 5. Navigation Focus State
- **Issue**: Focused navigation has gold background
- **Solution**: Remove background, use bold text instead
- **Impact**: Cleaner, more accessible focus indication

### 6. Navigation Container Alignment
- **Issue**: Navigation items not properly centred, theme toggle not far right
- **Solution**: Fix 1440px container alignment and positioning
- **Impact**: Better visual balance and proper spacing

## Technical Implementation Plan

### Phase 1: Analysis and Planning
1. **Current State Assessment**
   - Map current spacing values across components
   - Identify all components requiring changes
   - Document current behaviour for rollback

2. **Change Impact Analysis**
   - Assess responsive behaviour implications
   - Verify accessibility compliance maintained
   - Check for any breaking changes

### Phase 2: Grid and Spacing Changes

#### 2.1 Homepage Grid Layout (`src/app/(frontend)/page.tsx`)
```typescript
// Current gaps: gap-0 between main columns
// Target: Reduce internal spacing in grid system
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
  {/* Adjust internal padding values */}
  <aside className="... sm:pr-2 lg:pr-3 xl:pr-4 ..."> {/* Was pr-4/pr-6/pr-8 */}
  <section className="... sm:px-2 lg:px-3 xl:px-4 ..."> {/* Was px-4/px-6/px-8 */}
  <aside className="... xl:pl-4 ..."> {/* Was pl-8 */}
</div>
```

#### 2.2 Tier Section Spacing (`src/components/homepage/TierSection.tsx`)
```typescript
// Reduce spacing between cards in each tier
// Featured layout: mb-6 md:mb-8 → mb-3 md:mb-4
// Grid gaps: gap-4 md:gap-6 lg:gap-8 → gap-2 md:gap-3 lg:gap-4
// Horizontal layout: space-y-3 md:space-y-4 lg:space-y-6 → space-y-1.5 md:space-y-2 lg:space-y-3
```

### Phase 3: News Card Typography

#### 3.1 NewsCard Component (`src/components/NewsCard.tsx`)
```typescript
// CardContent component adjustments
// Title-description spacing: mt-2 → mt-1
// All CardDescription components get updated margin
<CardDescription className="mt-1 font-sans ..."> {/* Was mt-2 */}
```

### Phase 4: Trending Badge Removal

#### 4.1 NewsCard Trending Logic (`src/components/NewsCard.tsx`)
```typescript
// Force trending to false for all cards
const trending = false; // Was: article.publishedAt ? ... : false;

// Keep the trending logic for future use but disable display
// const trendingLogic = article.publishedAt ? ... : false; // Commented out
```

### Phase 5: "View More" Link Alignment

#### 5.1 TierSection Component (`src/components/homepage/TierSection.tsx`)
```typescript
// Change from center alignment to left alignment
// All "Show More" buttons: text-center → text-left
<div className="mt-6 text-left"> {/* Was text-center */}
<div className="mt-4 text-left"> {/* Was text-center */}
```

### Phase 6: Navigation Improvements

#### 6.1 Navigation Client Component (`src/components/navigation/navigation-client.tsx`)
```typescript
// Desktop navigation container fixes
<div className="flex w-full max-w-[1440px] items-center justify-between px-6">
  {/* Fix center alignment */}
  <div className="flex flex-1 items-center justify-center"> {/* Was justify-evenly */}
    <div className="flex items-center gap-8"> {/* New wrapper for proper spacing */}
      {items.map(item => (
        // Navigation items with proper spacing
      ))}
    </div>
  </div>
  {/* Theme toggle - ensure far right positioning */}
  <div className="flex-shrink-0"> {/* Was ml-6 */}
    <ModeToggle />
  </div>
</div>

// Focus state changes
className={`
  relative flex items-center transition-colors duration-200 
  text-sm/6 font-medium px-4 py-2 rounded-md
  hover:text-foreground focus:outline-none focus:ring-2 focus:ring-ring
  ${
    isActiveLink(item.href)
      ? 'text-foreground font-bold' // Remove bg-accent, add font-bold
      : 'text-muted-foreground hover:bg-accent/50 focus:font-bold' // Add focus:font-bold
  }
`}
```

## Implementation Steps

### Step 1: Preparation
1. **Create Feature Branch**: `git checkout -b frontend-style-improvements`
2. **Document Current State**: Screenshots and measurements
3. **Test Environment**: Ensure local development environment is ready

### Step 2: Component Updates (Priority Order)
1. **NewsCard Component**
   - Update typography spacing
   - Disable trending badges
   - Test all variants (default, title-only, horizontal)

2. **TierSection Component**
   - Update grid spacing
   - Align "View more" links to left
   - Test all three tiers

3. **Navigation Component**
   - Fix container alignment
   - Update focus states
   - Test desktop and mobile layouts

4. **Homepage Layout**
   - Reduce column spacing
   - Test responsive behaviour
   - Verify accessibility

### Step 3: Testing and Validation
1. **Visual Testing**
   - Screenshot comparison before/after
   - Test all screen sizes (mobile, tablet, desktop)
   - Verify proper spacing calculations

2. **Accessibility Testing**
   - Keyboard navigation still works
   - Focus indicators are clear
   - Screen reader compatibility

3. **Performance Testing**
   - No performance regressions
   - Proper component memoisation maintained

### Step 4: Quality Assurance
1. **Cross-browser Testing**
   - Chrome, Firefox, Safari
   - Mobile browsers
   - Responsive design validation

2. **Edge Cases**
   - Very long titles
   - Missing images
   - Empty states

3. **User Experience**
   - Improved content density
   - Better visual hierarchy
   - Maintained usability

## File Changes Required

### Primary Files
1. `src/components/NewsCard.tsx` - Typography and trending changes
2. `src/components/homepage/TierSection.tsx` - Grid spacing and alignment
3. `src/components/navigation/navigation-client.tsx` - Navigation improvements
4. `src/app/(frontend)/page.tsx` - Main grid layout adjustments

### Secondary Files
- `src/components/homepage/TierOneSection.tsx` - Verify spacing integration
- `src/components/homepage/TierTwoSection.tsx` - Verify spacing integration  
- `src/components/homepage/TierThreeSection.tsx` - Verify spacing integration

## Risk Assessment

### Low Risk Changes
- Typography spacing adjustments
- "View more" link alignment
- Trending badge removal

### Medium Risk Changes
- Grid spacing modifications (responsive impact)
- Navigation container alignment (layout impact)

### Mitigation Strategies
- Thorough responsive testing
- Gradual rollout capability
- Easy rollback with git
- Component-level testing

## Success Criteria

### Visual Improvements
- [x] 50% reduction in spacing between columns and cards
- [x] Tighter title-description spacing on all card variants
- [x] Trending badges completely hidden
- [x] "View more" links left-aligned
- [x] Clean navigation focus states (bold text, no background)
- [x] Properly centred navigation with far-right theme toggle

### Technical Requirements
- [x] No responsive breakage
- [x] Accessibility compliance maintained
- [x] Performance unaffected
- [x] Component memoisation preserved

### User Experience
- [x] Improved content density
- [x] Better visual hierarchy
- [x] Maintained usability
- [x] Cleaner visual appearance

## Timeline

### Day 1: Analysis and Setup
- Environment setup
- Current state documentation
- Change impact analysis

### Day 2: Core Implementation
- NewsCard component updates
- TierSection spacing changes
- Navigation improvements

### Day 3: Testing and Refinement
- Responsive testing
- Accessibility validation
- Cross-browser verification

### Day 4: Quality Assurance
- Edge case testing
- Performance validation
- User experience review

### Day 5: Deployment
- Final testing
- Production deployment
- Monitoring and feedback

## Rollback Plan

### Quick Rollback
1. **Git Revert**: `git revert <commit-hash>`
2. **Component Restore**: Restore individual components if needed
3. **Cache Clear**: Clear any build caches

### Gradual Rollback
1. **Feature Flag**: Add feature flag to toggle new styles
2. **A/B Testing**: Test both versions with users
3. **Selective Rollback**: Rollback only problematic changes

## Next Steps

1. **Review and Approve**: Team review of this implementation plan
2. **Technical Validation**: Ensure all changes are technically sound
3. **Design Review**: Verify changes align with design expectations
4. **Implementation Start**: Begin with Step 1 (Preparation)

---

**Note**: This plan focuses on visual improvements whilst maintaining accessibility, performance, and responsive behaviour. All changes are designed to be non-breaking and easily reversible.

description:
globs:
alwaysApply: false
---
