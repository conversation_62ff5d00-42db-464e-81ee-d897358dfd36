
When asked to write a plan, examine the existing plan file for any existing material (it may contain an outline or notes from the user, or a previous revision of the plan). Use the existing material as the foundation for your proposal.
Describe the specific code changes required concisely, with minimal surrounding prose. Design the code changes so that they can be implemented incrementally. Do not break up the changes too much: there should generally be two or three phases of work that logically go together, and can be stacked on each other. 
Any new or changed interfaces in your planned code should be well-typed, self-documenting, and self-consistent with surrounding code. If there are existing naming conventions, follow them. If you're creating a new file that contains a single method, the filename should match the method name (following our kebab-case convention in ts/tsx and snake_case in py). Method, parameter, and field names should be terse-but-descriptive, idiomatic to the language, and consistent with naming conventions in surrounding code.
If abstractions need to be adjusted for a clean, self-consistent end result, include the refactoring required as an independent phase before the implementation that uses these abstractions.
In each phase, look out for complex logic that has been added, and describe how it can be unit tested (describe the specific unit tests), searching for existing tests that can be updated, or describing new test files as needed. The unit tests should be grouped with the relevant phases to help us incrementally check our work. 
NEVER add any concluding errata (e.g. future considerations, next steps, etc), because ALL important future work should be handled in your plan. Instead, ALWAYS call out open questions if there are complex edge cases or considerations. Require additional guidance from me to write the highest quality plan possible. FLAG these clearly at the TOP of the plan.
DO NOT add any introductory or concluding remarks, I am ONLY interested in a concise overview specific code changes and I NEVER want to see irrelevant, unhelpful, redundant content. Examples of what NOT to include: "Benefits" concluding section (this is unhelpful), "Testing strategy" section (unit tests should be inlined with the appropriate plan phases), "Implementation summary" section (this is redundant). 
After you've written the implementation + unit test plan, add a section to the top of each phase that (1) lists the affected files and (2) concisely summarizes the changes in each file.
When asked to create or update the plan's task checklist, make sure the tasks are concise but specific one-liners that are self-consistent with the plan, ALWAYS at the top of the plan, organized by phase with checkboxes (☐) that can be marked as complete (☑) as you implement each part. ALWAYS update the plan to reflect the latest state of the implementation files.
