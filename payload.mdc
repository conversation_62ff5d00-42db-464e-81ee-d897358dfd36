# PayloadCMS Article Publication Validation - Technical Implementation

## Overview
This document provides detailed technical implementation for adding publication validation to the Articles collection in PayloadCMS. The validation ensures articles meet quality standards before being published.

## Current Collection Analysis

### Existing Articles Collection Structure
```typescript
// src/collections/Articles.ts
export const Articles: CollectionConfig = {
  slug: 'articles',
  fields: [
    // Core fields
    { name: 'title', type: 'text', required: true },
    { name: 'slug', type: 'text' },
    { name: 'featuredImage', type: 'upload', relationTo: 'media' }, // ✓ Already exists
    { name: 'workflowStage', type: 'select', options: [...] },
    
    // SEO Tab - from SEO plugin
    {
      label: 'SEO',
      name: 'meta',
      fields: [
        MetaTitleField({ hasGenerateFn: true }),        // ✓ Already exists
        MetaDescriptionField({ hasGenerateFn: true }),  // ✓ Already exists  
        MetaImageField({ relationTo: 'media', hasGenerateFn: true }), // ✓ Already exists
      ]
    }
  ]
}
```

### Current Validation Gaps
1. **No Featured Image Validation**: `featuredImage` field is optional
2. **No SEO Field Validation**: Meta fields can be empty when publishing
3. **No Workflow Stage Validation**: Articles can be published without meeting requirements

## Implementation Strategy

### Phase 1: Core Validation Logic

#### 1.1 Create Validation Utility Functions
```typescript
// src/collections/Articles/validation.ts
import type { PayloadRequest } from 'payload'
import type { Article } from '@/payload-types'

export interface PublicationValidationError {
  field: string
  message: string
  type: 'required' | 'invalid' | 'missing'
}

export interface ValidationResult {
  isValid: boolean
  errors: PublicationValidationError[]
  warnings: string[]
}

/**
 * Validate featured image requirement
 */
export const validateFeaturedImage = (data: Partial<Article>): PublicationValidationError | null => {
  if (!data.featuredImage) {
    return {
      field: 'featuredImage',
      message: 'Featured image is required before publishing this article',
      type: 'required'
    }
  }
  return null
}

/**
 * Validate SEO meta fields
 */
export const validateSEOFields = (data: Partial<Article>): PublicationValidationError[] => {
  const errors: PublicationValidationError[] = []
  
  // Check meta title
  if (!data.meta?.title || data.meta.title.trim() === '') {
    errors.push({
      field: 'meta.title',
      message: 'SEO title is required before publishing. Use the auto-generate button to create one.',
      type: 'required'
    })
  }
  
  // Check meta description
  if (!data.meta?.description || data.meta.description.trim() === '') {
    errors.push({
      field: 'meta.description', 
      message: 'SEO description is required before publishing. Use the auto-generate button to create one.',
      type: 'required'
    })
  }
  
  // Check meta image
  if (!data.meta?.image) {
    errors.push({
      field: 'meta.image',
      message: 'SEO meta image is required before publishing. Use the auto-generate button to create one.',
      type: 'required'
    })
  }
  
  return errors
}

/**
 * Validate SEO field quality using plugin's built-in checks
 */
export const validateSEOQuality = async (data: Partial<Article>, req: PayloadRequest): Promise<PublicationValidationError[]> => {
  const errors: PublicationValidationError[] = []
  
  // Check SEO title quality (length, keywords, etc.)
  if (data.meta?.title) {
    const titleLength = data.meta.title.length
    if (titleLength < 30 || titleLength > 60) {
      errors.push({
        field: 'meta.title',
        message: `SEO title should be 30-60 characters (current: ${titleLength}). Consider regenerating.`,
        type: 'invalid'
      })
    }
  }
  
  // Check SEO description quality
  if (data.meta?.description) {
    const descLength = data.meta.description.length
    if (descLength < 120 || descLength > 160) {
      errors.push({
        field: 'meta.description',
        message: `SEO description should be 120-160 characters (current: ${descLength}). Consider regenerating.`,
        type: 'invalid'
      })
    }
  }
  
  return errors
}

/**
 * Main publication validation function
 */
export const validatePublication = async (
  data: Partial<Article>, 
  req: PayloadRequest,
  operation: 'create' | 'update'
): Promise<ValidationResult> => {
  const errors: PublicationValidationError[] = []
  const warnings: string[] = []
  
  // Only validate if article is being published
  if (data.workflowStage !== 'published') {
    return { isValid: true, errors: [], warnings: [] }
  }
  
  // Validate featured image
  const featuredImageError = validateFeaturedImage(data)
  if (featuredImageError) {
    errors.push(featuredImageError)
  }
  
  // Validate SEO fields presence
  const seoErrors = validateSEOFields(data)
  errors.push(...seoErrors)
  
  // Validate SEO field quality
  const seoQualityErrors = await validateSEOQuality(data, req)
  errors.push(...seoQualityErrors)
  
  // Add warnings for quality improvements
  if (data.meta?.title && data.meta.title.length < 40) {
    warnings.push('SEO title could be longer for better performance')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Check if article was already published (grandfather clause)
 */
export const isAlreadyPublished = async (
  id: string | number,
  req: PayloadRequest
): Promise<boolean> => {
  if (!id) return false
  
  try {
    const existingArticle = await req.payload.findByID({
      collection: 'articles',
      id: id.toString(),
      req
    })
    
    return existingArticle?.workflowStage === 'published'
  } catch (error) {
    return false
  }
}
```

#### 1.2 Update Articles Collection with Validation Hooks
```typescript
// src/collections/Articles.ts - Updated with validation
import { 
  validatePublication, 
  isAlreadyPublished, 
  type ValidationResult 
} from './validation'

export const Articles: CollectionConfig = {
  slug: 'articles',
  // ... existing configuration
  
  hooks: {
    // ... existing hooks
    
    beforeChange: [
      // ... existing beforeChange hooks
      
      /**
       * Publication validation hook
       */
      async ({ data, req, operation, originalDoc }) => {
        try {
          // Only validate when transitioning to published
          if (data.workflowStage === 'published') {
            
            // Grandfather clause: skip validation for already published articles
            if (operation === 'update' && originalDoc?.workflowStage === 'published') {
              console.log(`⏭️ Skipping validation for already published article: ${originalDoc.title}`)
              return data
            }
            
            // Run validation
            const validationResult: ValidationResult = await validatePublication(data, req, operation)
            
            // Log validation results
            console.log(`🔍 Publication validation for article: ${data.title}`)
            console.log(`   Valid: ${validationResult.isValid}`)
            console.log(`   Errors: ${validationResult.errors.length}`)
            console.log(`   Warnings: ${validationResult.warnings.length}`)
            
            // Block publication if validation fails
            if (!validationResult.isValid) {
              const errorMessages = validationResult.errors.map(error => error.message)
              const combinedMessage = [
                'Article cannot be published due to the following issues:',
                ...errorMessages.map(msg => `• ${msg}`),
                '',
                'Please complete all required fields before publishing.'
              ].join('\n')
              
              throw new Error(combinedMessage)
            }
            
            // Log warnings (don't block publication)
            if (validationResult.warnings.length > 0) {
              console.log(`⚠️ Publication warnings:`)
              validationResult.warnings.forEach(warning => {
                console.log(`   - ${warning}`)
              })
            }
            
            console.log(`✅ Article validation passed: ${data.title}`)
          }
          
          return data
        } catch (error) {
          console.error('❌ Publication validation error:', error)
          throw error // Re-throw to block save
        }
      }
    ],
    
    beforeValidate: [
      // ... existing beforeValidate hooks
      
      /**
       * Set published metadata when moving to published status
       */
      async ({ data, operation, req }) => {
        try {
          if (operation === 'update' && data?.workflowStage === 'published') {
            // Set published metadata only if not already set
            if (!data.publishedAt) {
              data.publishedAt = new Date().toISOString()
            }
            if (!data.publishedBy && req?.user?.id) {
              data.publishedBy = req.user.id
            }
          }
          return data
        } catch (error) {
          console.error('Error in beforeValidate hook:', error)
          return data
        }
      }
    ]
  }
}
```

### Phase 2: Admin Interface Enhancements

#### 2.1 Create Publication Status Component
```typescript
// src/components/admin/publication-status/PublicationStatus.tsx
'use client'

import React from 'react'
import { useField } from 'payload/forms'
import { Check, X, AlertCircle } from 'lucide-react'

interface PublicationStatusProps {
  path: string
}

interface ValidationCheck {
  field: string
  label: string
  isValid: boolean
  message?: string
}

export const PublicationStatus: React.FC<PublicationStatusProps> = ({ path }) => {
  const { value: workflowStage } = useField({ path: 'workflowStage' })
  const { value: featuredImage } = useField({ path: 'featuredImage' })
  const { value: metaTitle } = useField({ path: 'meta.title' })
  const { value: metaDescription } = useField({ path: 'meta.description' })
  const { value: metaImage } = useField({ path: 'meta.image' })
  
  // Don't show if not trying to publish
  if (workflowStage !== 'published') {
    return null
  }
  
  const checks: ValidationCheck[] = [
    {
      field: 'featuredImage',
      label: 'Featured Image',
      isValid: !!featuredImage,
      message: !featuredImage ? 'Please upload a featured image' : undefined
    },
    {
      field: 'meta.title',
      label: 'SEO Title',
      isValid: !!metaTitle && metaTitle.trim() !== '',
      message: !metaTitle ? 'Please generate or enter an SEO title' : undefined
    },
    {
      field: 'meta.description',
      label: 'SEO Description',
      isValid: !!metaDescription && metaDescription.trim() !== '',
      message: !metaDescription ? 'Please generate or enter an SEO description' : undefined
    },
    {
      field: 'meta.image',
      label: 'SEO Meta Image',
      isValid: !!metaImage,
      message: !metaImage ? 'Please generate or select an SEO meta image' : undefined
    }
  ]
  
  const failedChecks = checks.filter(check => !check.isValid)
  const allValid = failedChecks.length === 0
  
  return (
    <div className="publication-status">
      <div className={`status-header ${allValid ? 'valid' : 'invalid'}`}>
        {allValid ? (
          <div className="status-valid">
            <Check className="status-icon" />
            <span>Ready to Publish</span>
          </div>
        ) : (
          <div className="status-invalid">
            <X className="status-icon" />
            <span>Publication Requirements Not Met</span>
          </div>
        )}
      </div>
      
      <div className="status-checks">
        {checks.map(check => (
          <div key={check.field} className={`check-item ${check.isValid ? 'valid' : 'invalid'}`}>
            {check.isValid ? (
              <Check className="check-icon valid" />
            ) : (
              <X className="check-icon invalid" />
            )}
            <span className="check-label">{check.label}</span>
            {!check.isValid && check.message && (
              <div className="check-message">
                <AlertCircle className="message-icon" />
                <span>{check.message}</span>
              </div>
            )}
          </div>
        ))}
      </div>
      
      {!allValid && (
        <div className="status-help">
          <p>Complete all requirements above before publishing. Use the auto-generate buttons in the SEO tab to quickly create missing content.</p>
        </div>
      )}
    </div>
  )
}
```

#### 2.2 Add Publication Status to Articles Collection
```typescript
// src/collections/Articles.ts - Add UI component
export const Articles: CollectionConfig = {
  // ... existing configuration
  
  fields: [
    // ... existing fields
    
    // Add publication status component near workflow stage
    {
      name: 'workflowStage',
      type: 'select',
      required: true,
      defaultValue: 'candidate-article',
      options: [
        { label: 'Candidate Article', value: 'candidate-article' },
        { label: 'Translated', value: 'translated' },
        { label: 'Ready for Review', value: 'ready-for-review' },
        { label: 'Published', value: 'published' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Current stage in the article workflow and publication status',
      },
    },
    
    // Publication status indicator
    {
      type: 'ui',
      name: 'publicationStatus',
      admin: {
        position: 'sidebar',
        components: {
          Field: '@/components/admin/publication-status/PublicationStatus#PublicationStatus',
        },
      },
    },
    
    // ... rest of fields
  ]
}
```

### Phase 3: Error Handling and User Experience

#### 3.1 Enhanced Error Messages
```typescript
// src/collections/Articles/errors.ts
export class PublicationValidationError extends Error {
  public field: string
  public userMessage: string
  public suggestions: string[]
  
  constructor(field: string, userMessage: string, suggestions: string[] = []) {
    super(`Publication validation failed for field: ${field}`)
    this.field = field
    this.userMessage = userMessage
    this.suggestions = suggestions
    this.name = 'PublicationValidationError'
  }
}

export const createPublicationError = (errors: PublicationValidationError[]): Error => {
  const mainMessage = 'Article cannot be published due to the following requirements:'
  
  const formattedErrors = errors.map(error => {
    const suggestions = error.suggestions.length > 0 
      ? `\n  Suggestions: ${error.suggestions.join(', ')}`
      : ''
    
    return `• ${error.userMessage}${suggestions}`
  })
  
  const fullMessage = [
    mainMessage,
    ...formattedErrors,
    '',
    'Please complete all required fields before publishing.',
    'Use the auto-generate buttons in the SEO tab to quickly create missing content.'
  ].join('\n')
  
  return new Error(fullMessage)
}
```

#### 3.2 Updated Validation with Better Errors
```typescript
// src/collections/Articles/validation.ts - Enhanced error handling
export const validatePublication = async (
  data: Partial<Article>, 
  req: PayloadRequest,
  operation: 'create' | 'update'
): Promise<ValidationResult> => {
  const errors: PublicationValidationError[] = []
  
  // Featured image validation with suggestions
  if (!data.featuredImage) {
    errors.push(new PublicationValidationError(
      'featuredImage',
      'Featured image is required before publishing',
      ['Upload an image in the Featured Image field', 'Use a relevant financial/business image']
    ))
  }
  
  // SEO title validation with suggestions
  if (!data.meta?.title || data.meta.title.trim() === '') {
    errors.push(new PublicationValidationError(
      'meta.title',
      'SEO title is required before publishing',
      ['Click "Auto-generate" in the SEO tab', 'Write a compelling 40-60 character title']
    ))
  }
  
  // SEO description validation with suggestions
  if (!data.meta?.description || data.meta.description.trim() === '') {
    errors.push(new PublicationValidationError(
      'meta.description',
      'SEO description is required before publishing',
      ['Click "Auto-generate" in the SEO tab', 'Write a 120-160 character description']
    ))
  }
  
  // SEO meta image validation with suggestions
  if (!data.meta?.image) {
    errors.push(new PublicationValidationError(
      'meta.image',
      'SEO meta image is required before publishing',
      ['Click "Auto-generate" in the SEO tab', 'Select the featured image or upload a specific meta image']
    ))
  }
  
  // Throw enhanced error if validation fails
  if (errors.length > 0) {
    throw createPublicationError(errors)
  }
  
  return { isValid: true, errors: [], warnings: [] }
}
```

### Phase 4: Testing and Safety

#### 4.1 Database Migration Safety
```typescript
// src/collections/Articles/migration.ts
export const ensureExistingArticlesSafety = async (payload: any) => {
  console.log('🔍 Checking existing published articles for validation compatibility...')
  
  const publishedArticles = await payload.find({
    collection: 'articles',
    where: {
      workflowStage: { equals: 'published' }
    },
    limit: 1000
  })
  
  console.log(`📊 Found ${publishedArticles.totalDocs} published articles`)
  
  let validCount = 0
  let invalidCount = 0
  
  for (const article of publishedArticles.docs) {
    const hasRequiredFields = !!(
      article.featuredImage &&
      article.meta?.title &&
      article.meta?.description &&
      article.meta?.image
    )
    
    if (hasRequiredFields) {
      validCount++
    } else {
      invalidCount++
      console.log(`⚠️ Article "${article.title}" (ID: ${article.id}) missing required fields`)
    }
  }
  
  console.log(`✅ Valid articles: ${validCount}`)
  console.log(`⚠️ Invalid articles: ${invalidCount}`)
  
  if (invalidCount > 0) {
    console.log('📝 Invalid articles will be grandfathered and remain accessible')
  }
}
```

#### 4.2 Feature Flag Implementation
```typescript
// src/collections/Articles/config.ts
export const PUBLICATION_VALIDATION_CONFIG = {
  enabled: process.env.PUBLICATION_VALIDATION_ENABLED !== 'false',
  grandfatherExisting: true,
  strictMode: process.env.NODE_ENV === 'production',
  logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'info'
}

// In validation hook
if (!PUBLICATION_VALIDATION_CONFIG.enabled) {
  console.log('⏭️ Publication validation is disabled')
  return data
}
```

### Phase 5: Deployment Strategy

#### 5.1 Pre-deployment Checklist
```bash
# 1. Database backup
npm run db:backup

# 2. Run safety check
npm run articles:check-existing

# 3. Test validation in development
npm run test:publication-validation

# 4. Deploy with feature flag disabled
PUBLICATION_VALIDATION_ENABLED=false npm run deploy

# 5. Enable validation after verification
# Set PUBLICATION_VALIDATION_ENABLED=true in environment
```

#### 5.2 Monitoring and Rollback
```typescript
// src/collections/Articles/monitoring.ts
export const logPublicationValidation = (
  articleId: string,
  title: string,
  validationResult: ValidationResult,
  operation: 'create' | 'update'
) => {
  const logData = {
    timestamp: new Date().toISOString(),
    articleId,
    title,
    operation,
    valid: validationResult.isValid,
    errors: validationResult.errors.length,
    warnings: validationResult.warnings.length
  }
  
  // Log to monitoring service
  console.log('📊 Publication validation:', JSON.stringify(logData))
  
  // Track metrics
  if (validationResult.isValid) {
    // Increment success counter
  } else {
    // Increment failure counter
    // Alert if failure rate > threshold
  }
}
```

## File Structure

```
src/
├── collections/
│   ├── Articles/
│   │   ├── index.ts              # Main collection config
│   │   ├── validation.ts         # Validation logic
│   │   ├── errors.ts            # Error handling
│   │   ├── migration.ts         # Safety checks
│   │   ├── monitoring.ts        # Logging/monitoring
│   │   └── config.ts            # Feature flags
│   └── Articles.ts              # Updated collection
├── components/
│   └── admin/
│       └── publication-status/
│           ├── PublicationStatus.tsx
│           └── index.ts
└── styles/
    └── admin/
        └── publication-status.css
```

## Backward Compatibility

### Existing Data Safety
- **No schema changes**: All validation works with existing fields
- **Grandfather clause**: Existing published articles bypass validation
- **Graceful degradation**: System works even if validation fails

### Migration Strategy
- **Phase 1**: Deploy with validation disabled
- **Phase 2**: Enable validation for new articles only
- **Phase 3**: Full validation for all articles (optional)

## Next Steps

1. **Implementation**: Start with Phase 1 (Core Validation Logic)
2. **Testing**: Comprehensive testing in development environment
3. **Deployment**: Gradual rollout with monitoring
4. **Monitoring**: Track validation success rates and user feedback
5. **Refinement**: Iterate based on user experience

---

**Note**: This implementation prioritizes data safety and user experience while providing comprehensive validation for publication quality standards.
description:
globs:
alwaysApply: false
---
